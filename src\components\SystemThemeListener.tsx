import React, { useEffect, useMemo, useRef } from 'react';
import { AppState, AppStateStatus } from 'react-native';
import { useAppStore } from '../stores/appStore';

/**
 * SystemThemeListener 组件
 * 管理系统主题监听器的生命周期，包括注册、清理和错误处理
 * 确保主题变化的即时响应和流畅性
 */
export const SystemThemeListener: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const {
    theme,
    isDarkMode,
    startSystemThemeListener,
    stopSystemThemeListener,
    isSystemThemeListening,
    _handleSystemThemeChangeInternal
  } = useAppStore();
  
  // 使用 ref 来跟踪组件是否已挂载，避免在组件卸载后设置状态
  const isMountedRef = useRef(true);
  const previousThemeRef = useRef(theme);
  const previousIsDarkModeRef = useRef(isDarkMode);
  const appStateRef = useRef(AppState.currentState);
  
  // 使用 useMemo 来优化性能，避免不必要的重新计算
  const shouldStartListener = useMemo(() => theme === 'system', [theme]);
  
  // 处理应用状态变化
  const handleAppStateChange = (nextAppState: AppStateStatus) => {
    if (theme === 'system') {
      // 当应用从后台回到前台时，检查系统主题是否发生变化
      if (
        appStateRef.current.match(/inactive|background/) && 
        nextAppState === 'active'
      ) {
        console.log('App has come to the foreground, checking system theme');
        // 获取当前系统主题并处理变化
        _handleSystemThemeChangeInternal(null);
      }
      appStateRef.current = nextAppState;
    }
  };
  
  // 主题变化监听效果
  useEffect(() => {
    if (!isMountedRef.current) return;
    
    if (shouldStartListener) {
      try {
        startSystemThemeListener();
        console.log('System theme listener activated for immediate response');
      } catch (error) {
        // 优雅处理监听器注册失败的情况
        console.warn('Failed to start system theme listener:', error);
        // 这里可以添加用户友好的错误处理，比如显示通知
        // 但不会阻止应用的正常运行
      }
    } else {
      // 如果主题不是 "system"，确保停止监听器
      if (isSystemThemeListening) {
        try {
          stopSystemThemeListener();
          console.log('System theme listener deactivated');
        } catch (error) {
          console.warn('Failed to stop system theme listener:', error);
        }
      }
    }
    
    // 更新引用值
    previousThemeRef.current = theme;
  }, [theme, shouldStartListener, startSystemThemeListener, stopSystemThemeListener, isSystemThemeListening]);
  
  // 监听主题模式变化，确保UI组件立即更新
  useEffect(() => {
    if (!isMountedRef.current) return;
    
    // 检查主题是否实际发生了变化
    if (previousIsDarkModeRef.current !== isDarkMode) {
      // 强制触发一次重新渲染，确保所有UI组件立即响应主题变化
      // 这里可以添加额外的逻辑来确保状态栏等系统UI元素同步更新
      console.log(`Theme mode changed to: ${isDarkMode ? 'dark' : 'light'} - ensuring immediate UI update`);
      
      // 更新引用值
      previousIsDarkModeRef.current = isDarkMode;
    }
  }, [isDarkMode]);
  
  // 监听应用状态变化
  useEffect(() => {
    const subscription = AppState.addEventListener('change', handleAppStateChange);
    
    return () => {
      subscription?.remove();
    };
  }, [theme, _handleSystemThemeChangeInternal]);
  
  // 组件卸载时清理监听器，避免内存泄漏
  useEffect(() => {
    return () => {
      isMountedRef.current = false;
      try {
        stopSystemThemeListener();
        console.log('System theme listener cleaned up on component unmount');
      } catch (error) {
        console.warn('Failed to cleanup system theme listener on unmount:', error);
      }
    };
  }, [stopSystemThemeListener]);
  
  return <>{children}</>;
};