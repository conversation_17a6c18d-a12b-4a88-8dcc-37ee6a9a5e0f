/**
 * 密码加密工具
 * 支持SHA256哈希和RSA公钥加密
 */

import { SHA256 } from "crypto-js";
import forge from "node-forge";

/**
 * RSA公钥（与参考项目保持一致）
 */
const RSA_PUBLIC_KEY =
  "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAieA5KkjkN1YMoVMtoI2L0nj9yHYuVdc2WskNEn/afpULcG31iGBoN2DBGKkP6sdOIzGo9mtSZIg2mXe1SC8DJkEj5gmLw7AzSiuttw5wqo1Rdo35yVAmzpN9Ix6fZ8DIjH3jvEHF2nPh9X+gZycRSB4Qty3WsvOjq3fOAujHeE1HiOc8iz21Y5U4yED+sy49awuHSFnHXAJ4MedHRSKeYkXsbcRxcgNwGYAoGXkFgwI2yJSBGuLO+tIL626FbWKUZmxmeEtdE3KvKKmPWU9m4ugV5xl8gReucStmuoKhRb11gh0cvphOcM6/GPhq1BQJP1vmqGn2tmhL5UUYjVfILQIDAQAB";

/**
 * 加密模式枚举
 */
export enum EncryptionMode {
  SHA256_ONLY = "sha256",
  RSA_ONLY = "rsa",
  SHA256_THEN_RSA = "sha256_rsa",
}

/**
 * RSA填充模式枚举
 */
export enum RSAPaddingMode {
  PKCS1 = "RSAES-PKCS1-V1_5",
  OAEP = "RSA-OAEP",
}

/**
 * 使用SHA256哈希加密密码
 * @param password 原始密码
 * @returns 加密后的密码字符串
 */
export const encryptPasswordWithSHA256 = (password: string): string => {
  const encryptedPassword = SHA256(password).toString();
  return encryptedPassword;
};

/**
 * 使用RSA公钥加密数据
 * @param data 要加密的数据
 * @param paddingMode RSA填充模式，默认为PKCS1
 * @returns 加密后的base64字符串
 */
export const encryptWithRSA = (
  data: string,
  paddingMode: RSAPaddingMode = RSAPaddingMode.PKCS1
): string => {
  try {
    // 将公钥字符串转换为PEM格式
    const pemKey = `-----BEGIN PUBLIC KEY-----\n${RSA_PUBLIC_KEY}\n-----END PUBLIC KEY-----`;

    // 解析公钥
    const publicKey = forge.pki.publicKeyFromPem(pemKey);

    let encrypted: string;

    if (paddingMode === RSAPaddingMode.PKCS1) {
      // 使用PKCS#1 v1.5填充模式
      encrypted = publicKey.encrypt(data, "RSAES-PKCS1-V1_5");
    } else {
      // 使用RSA-OAEP填充模式
      encrypted = publicKey.encrypt(data, "RSA-OAEP", {
        md: forge.md.sha256.create(),
        mgf: forge.mgf.mgf1.create(forge.md.sha256.create()),
      });
    }

    // 转换为base64
    return forge.util.encode64(encrypted);
  } catch (error) {
    throw new Error(
      `RSA加密失败: ${error instanceof Error ? error.message : "未知错误"}`
    );
  }
};

/**
 * 验证密码是否为空或无效
 * @param password 密码
 * @returns 是否有效
 */
export const isValidPassword = (password: string): boolean => {
  return Boolean(password && password.trim().length > 0);
};

/**
 * 加密工具类
 * 提供统一的加密接口
 */
export class EncryptionUtils {
  /**
   * 获取RSA公钥信息
   * @returns 公钥信息对象
   */
  static getPublicKeyInfo() {
    return {
      key: RSA_PUBLIC_KEY,
      format: "PKCS#8",
      keySize: 2048,
      supportedPadding: ["RSAES-PKCS1-V1_5", "RSA-OAEP"],
      defaultPadding: "RSAES-PKCS1-V1_5",
    };
  }

  /**
   * 加密密码（默认使用RSA PKCS#1加密）
   * @param password 原始密码
   * @param mode 加密模式，默认为RSA_ONLY
   * @param paddingMode RSA填充模式，默认为PKCS1
   * @returns Promise<string> 加密后的密码
   */
  static async encryptPassword(
    password: string,
    mode: EncryptionMode = EncryptionMode.RSA_ONLY,
    paddingMode: RSAPaddingMode = RSAPaddingMode.PKCS1
  ): Promise<string> {
    if (!isValidPassword(password)) {
      throw new Error("密码不能为空");
    }

    switch (mode) {
      case EncryptionMode.SHA256_ONLY:
        return encryptPasswordWithSHA256(password);

      case EncryptionMode.RSA_ONLY:
        return encryptWithRSA(password, paddingMode);

      case EncryptionMode.SHA256_THEN_RSA:
        const hashedPassword = encryptPasswordWithSHA256(password);
        return encryptWithRSA(hashedPassword, paddingMode);

      default:
        throw new Error(`不支持的加密模式: ${mode}`);
    }
  }

  /**
   * 兼容性方法：使用SHA256加密（保持向后兼容）
   * @param password 原始密码
   * @returns Promise<string> SHA256哈希后的密码
   */
  static async encryptPasswordLegacy(password: string): Promise<string> {
    return this.encryptPassword(password, EncryptionMode.SHA256_ONLY);
  }
}
