/**
 * 全局加载状态管理
 * 用于显示全局加载指示器
 */

import { create } from 'zustand';
import { LoadingState } from '../types';

interface LoadingItem {
  id: string;
  message: string;
  timestamp: number;
}

interface LoadingStore {
  // 状态
  isLoading: boolean;
  loadingItems: LoadingItem[];
  globalMessage: string;
  
  // 操作方法
  showLoading: (message?: string, id?: string) => string;
  hideLoading: (id?: string) => void;
  hideAllLoading: () => void;
  setGlobalMessage: (message: string) => void;
  
  // 内部方法
  addLoadingItem: (item: LoadingItem) => void;
  removeLoadingItem: (id: string) => void;
  updateLoadingState: () => void;
}

export const useLoadingStore = create<LoadingStore>((set, get) => ({
  // 初始状态
  isLoading: false,
  loadingItems: [],
  globalMessage: '',

  // 显示加载
  showLoading: (message: string = '加载中...', id?: string): string => {
    const loadingId = id || `loading_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const newItem: LoadingItem = {
      id: loadingId,
      message,
      timestamp: Date.now(),
    };
    
    get().addLoadingItem(newItem);
    return loadingId;
  },

  // 隐藏加载
  hideLoading: (id?: string) => {
    if (id) {
      get().removeLoadingItem(id);
    } else {
      // 如果没有指定ID，移除最新的加载项
      const { loadingItems } = get();
      if (loadingItems.length > 0) {
        const latestItem = loadingItems[loadingItems.length - 1];
        get().removeLoadingItem(latestItem.id);
      }
    }
  },

  // 隐藏所有加载
  hideAllLoading: () => {
    set({ 
      loadingItems: [], 
      isLoading: false, 
      globalMessage: '' 
    });
  },

  // 设置全局消息
  setGlobalMessage: (message: string) => {
    set({ globalMessage: message });
  },

  // 添加加载项
  addLoadingItem: (item: LoadingItem) => {
    const { loadingItems } = get();
    const newItems = [...loadingItems, item];
    set({ loadingItems: newItems });
    get().updateLoadingState();
  },

  // 移除加载项
  removeLoadingItem: (id: string) => {
    const { loadingItems } = get();
    const newItems = loadingItems.filter(item => item.id !== id);
    set({ loadingItems: newItems });
    get().updateLoadingState();
  },

  // 更新加载状态
  updateLoadingState: () => {
    const { loadingItems } = get();
    const isLoading = loadingItems.length > 0;
    const globalMessage = loadingItems.length > 0 
      ? loadingItems[loadingItems.length - 1].message 
      : '';
    
    set({ isLoading, globalMessage });
  },
}));

// 导出便捷的Hook
export const useLoading = () => {
  const { isLoading, globalMessage, showLoading, hideLoading, hideAllLoading } = useLoadingStore();
  
  return {
    isLoading,
    message: globalMessage,
    show: showLoading,
    hide: hideLoading,
    hideAll: hideAllLoading,
  };
};

// 导出异步操作包装器
export const withLoading = async <T>(
  asyncFn: () => Promise<T>,
  message: string = '处理中...'
): Promise<T> => {
  const { showLoading, hideLoading } = useLoadingStore.getState();
  
  const loadingId = showLoading(message);
  
  try {
    const result = await asyncFn();
    return result;
  } finally {
    hideLoading(loadingId);
  }
};
