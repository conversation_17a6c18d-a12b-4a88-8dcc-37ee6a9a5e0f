/**
 * Button 组件
 * 可定制的按钮组件，支持多种样式和状态
 */

import { LinearGradient } from 'expo-linear-gradient';
import React from 'react';
import {
    ActivityIndicator,
    Text,
    TextStyle,
    TouchableOpacity,
    ViewStyle
} from 'react-native';
import { useTheme } from '../../stores/appStore';
import { ButtonProps } from '../../types';

const Button: React.FC<ButtonProps> = ({
  title,
  onPress,
  disabled = false,
  loading = false,
  variant = 'primary',
  size = 'medium',
  style,
  testID,
}) => {
  const { theme } = useTheme();

  // 获取按钮样式
  const getButtonStyle = (): ViewStyle => {
    const baseStyle: ViewStyle = {
      borderRadius: theme.borderRadius.base,
      alignItems: 'center',
      justifyContent: 'center',
      flexDirection: 'row',
    };

    // 尺寸样式
    const sizeStyles: Record<string, ViewStyle> = {
      small: {
        paddingHorizontal: theme.spacing[3],
        paddingVertical: theme.spacing[2],
        minHeight: 32,
      },
      medium: {
        paddingHorizontal: theme.spacing[4],
        paddingVertical: theme.spacing[3],
        minHeight: 40,
      },
      large: {
        paddingHorizontal: theme.spacing[6],
        paddingVertical: theme.spacing[4],
        minHeight: 48,
      },
    };

    // 变体样式
    const variantStyles: Record<string, ViewStyle> = {
      primary: {
        backgroundColor: disabled ? theme.colors.border.primary : theme.colors.primary,
        ...theme.shadows.sm,
      },
      secondary: {
        backgroundColor: disabled ? theme.colors.border.secondary : theme.colors.surface.secondary,
        borderWidth: 1,
        borderColor: disabled ? theme.colors.border.primary : theme.colors.border.primary,
      },
      outline: {
        backgroundColor: 'transparent',
        borderWidth: 1,
        borderColor: disabled ? theme.colors.border.primary : theme.colors.primary,
      },
      text: {
        backgroundColor: 'transparent',
      },
    };

    return {
      ...baseStyle,
      ...sizeStyles[size],
      ...variantStyles[variant],
    };
  };

  // 获取文字样式
  const getTextStyle = (): TextStyle => {
    const baseStyle: TextStyle = {
      fontFamily: theme.typography.fontFamily.primary,
      fontWeight: theme.typography.fontWeight.medium,
      textAlign: 'center',
    };

    // 尺寸样式
    const sizeStyles: Record<string, TextStyle> = {
      small: {
        fontSize: theme.typography.fontSize.sm,
      },
      medium: {
        fontSize: theme.typography.fontSize.base,
      },
      large: {
        fontSize: theme.typography.fontSize.lg,
      },
    };

    // 变体样式
    const variantStyles: Record<string, TextStyle> = {
      primary: {
        color: disabled ? theme.colors.text.disabled : theme.colors.text.inverse,
      },
      secondary: {
        color: disabled ? theme.colors.text.disabled : theme.colors.text.primary,
      },
      outline: {
        color: disabled ? theme.colors.text.disabled : theme.colors.primary,
      },
      text: {
        color: disabled ? theme.colors.text.disabled : theme.colors.primary,
      },
    };

    return {
      ...baseStyle,
      ...sizeStyles[size],
      ...variantStyles[variant],
    };
  };

  const handlePress = () => {
    if (!disabled && !loading && onPress) {
      onPress();
    }
  };

  // 渲染按钮内容
  const renderButtonContent = () => (
    <>
      {loading && (
        <ActivityIndicator
          size="small"
          color={variant === 'primary' ? theme.colors.text.inverse : theme.colors.primary}
          style={{ marginRight: theme.spacing[2] }}
        />
      )}
      <Text style={getTextStyle()}>{title}</Text>
    </>
  );

  // 如果是主要按钮，使用渐变背景
  if (variant === 'primary') {
    return (
      <TouchableOpacity
        style={[getButtonStyle(), { backgroundColor: 'transparent', borderWidth: 0 }, style]}
        onPress={handlePress}
        disabled={disabled || loading}
        activeOpacity={0.8}
        testID={testID}
      >
        <LinearGradient
          colors={disabled ? ['#E4E7ED', '#E4E7ED'] : ['#667eea', '#409EFF']}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
          style={{
            ...getButtonStyle(),
            backgroundColor: 'transparent',
            borderWidth: 0,
            margin: 0,
            padding: 0,
            paddingHorizontal: theme.spacing[size === 'small' ? 3 : size === 'medium' ? 4 : 6],
            paddingVertical: theme.spacing[size === 'small' ? 2 : size === 'medium' ? 3 : 4],
          }}
        >
          {renderButtonContent()}
        </LinearGradient>
      </TouchableOpacity>
    );
  }

  return (
    <TouchableOpacity
      style={[getButtonStyle(), style]}
      onPress={handlePress}
      disabled={disabled || loading}
      activeOpacity={0.7}
      testID={testID}
    >
      {renderButtonContent()}
    </TouchableOpacity>
  );
};

export default Button;
