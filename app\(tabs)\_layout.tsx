/**
 * Tabs 布局组件
 * 配置底部 Tab 导航
 */

import FontAwesome from '@expo/vector-icons/FontAwesome';
import { Tabs } from 'expo-router';
import React from 'react';
import { useTheme } from '../../src/stores/appStore';

export default function TabLayout() {
  const { theme } = useTheme();

  const tabBarStyle = {
    backgroundColor: theme.isDark ? '#1E1E1E' : '#FFFFFF',
    borderTopWidth: 1,
    borderTopColor: theme.isDark ? '#404040' : '#E5E7EB',
    height: 85,
    paddingBottom: 25,
    paddingTop: 10,
    paddingHorizontal: 10,
  };

  return (
    <Tabs
      screenOptions={{
        tabBarActiveTintColor: '#007BFF',
        tabBarInactiveTintColor: theme.isDark ? '#B3B3B3' : '#6B7280',
        tabBarStyle,
        tabBarLabelStyle: {
          fontSize: 14,
          fontWeight: '500',
        },
        headerShown: false,
      }}
    >
      <Tabs.Screen
        name="index"
        options={{
          title: '首页',
          tabBarIcon: ({ color, size }) => (
            <FontAwesome size={size || 24} name="home" color={color} />
          ),
        }}
      />
      <Tabs.Screen
        name="work"
        options={{
          title: '工作',
          tabBarIcon: ({ color, size }) => (
            <FontAwesome size={size || 24} name="briefcase" color={color} />
          ),
        }}
      />
      <Tabs.Screen
        name="profile"
        options={{
          title: '我的',
          tabBarIcon: ({ color, size }) => (
            <FontAwesome size={size || 24} name="user" color={color} />
          ),
        }}
      />
    </Tabs>
  );
}