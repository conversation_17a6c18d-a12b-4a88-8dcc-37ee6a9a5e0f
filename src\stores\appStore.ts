/**
 * 应用配置状态管理
 * 管理主题、语言、全局配置等
 */

import AsyncStorage from "@react-native-async-storage/async-storage";
import { Appearance } from "react-native";
import { create } from "zustand";
import { API_CONFIG, STORAGE_KEYS } from "../constants";
import { AppConfig, Language, ThemeMode } from "../types";
import { debounce } from "../utils";

// 检查 Appearance API 是否可用
const isAppearanceAPIAvailable = (): boolean => {
  try {
    return !!(Appearance && typeof Appearance.getColorScheme === 'function' && typeof Appearance.addChangeListener === 'function');
  } catch (error) {
    console.warn("Appearance API availability check failed:", error);
    return false;
  }
};

// 获取系统主题
const getSystemTheme = (): "light" | "dark" => {
  try {
    if (isAppearanceAPIAvailable()) {
      const colorScheme = Appearance.getColorScheme();
      return colorScheme === "dark" ? "dark" : "light";
    } else {
      console.warn("Appearance API not available, falling back to light theme");
    }
  } catch (error) {
    console.error("Failed to get system theme:", error);
  }
  return "light"; // 默认返回浅色主题
};

// 根据主题模式获取实际主题
const getActualTheme = (themeMode: ThemeMode): "light" | "dark" => {
  if (themeMode === "system") {
    return getSystemTheme();
  }
  return themeMode;
};

interface AppState {
  // 主题配置
  theme: ThemeMode;
  isDarkMode: boolean;

  // 语言配置
  language: Language;

  // 应用配置
  config: AppConfig;

  // 全局状态
  isInitialized: boolean;
  isOnline: boolean;

  // 系统主题监听状态
  systemThemeListener: any | null;
  isSystemThemeListening: boolean;

  // 操作方法
  setTheme: (theme: ThemeMode) => Promise<void>;
  toggleTheme: () => Promise<void>;
  setLanguage: (language: Language) => Promise<void>;
  setOnlineStatus: (isOnline: boolean) => void;
  initializeApp: () => Promise<void>;
  updateConfig: (config: Partial<AppConfig>) => Promise<void>;

  // 系统主题监听方法
  handleSystemThemeChange: (colorScheme: any) => void;
  _handleSystemThemeChangeInternal: (colorScheme: any) => void;
  startSystemThemeListener: () => void;
  stopSystemThemeListener: () => void;

  // 内部方法
  loadStoredConfig: () => Promise<void>;
  saveConfig: () => Promise<void>;
}

export const useAppStore = create<AppState>((set, get) => ({
  // 初始状态
  theme: "light",
  isDarkMode: false,
  language: "zh-CN",
  config: {
    theme: "light",
    language: "zh-CN",
    apiUrl: API_CONFIG.BASE_URL,
    timeout: API_CONFIG.TIMEOUT,
  },
  isInitialized: false,
  isOnline: true,
  systemThemeListener: null,
  isSystemThemeListening: false,

  // 设置主题
  setTheme: async (theme: ThemeMode) => {
    try {
      const currentTheme = get().theme;
      const actualTheme = getActualTheme(theme);

      set({
        theme,
        isDarkMode: actualTheme === "dark",
        config: { ...get().config, theme },
      });

      // 根据主题模式控制系统主题监听器
      if (theme === "system") {
        // 切换到"跟随系统"模式时启动监听器
        get().startSystemThemeListener();
      } else if (currentTheme === "system") {
        // 从"跟随系统"模式切换到固定主题时停止监听器
        get().stopSystemThemeListener();
      }

      await AsyncStorage.setItem(
        STORAGE_KEYS.THEME_CONFIG,
        JSON.stringify({
          theme,
          isDarkMode: actualTheme === "dark",
        })
      );

      await get().saveConfig();
    } catch (error) {
      console.error("Failed to set theme:", error);
    }
  },

  // 切换主题 (循环切换: light -> dark -> system -> light)
  toggleTheme: async () => {
    const { theme, setTheme } = get();
    let newTheme: ThemeMode;
    switch (theme) {
      case "light":
        newTheme = "dark";
        break;
      case "dark":
        newTheme = "system";
        break;
      case "system":
        newTheme = "light";
        break;
      default:
        newTheme = "light";
    }
    await setTheme(newTheme);
  },

  // 设置语言
  setLanguage: async (language: Language) => {
    try {
      set({
        language,
        config: { ...get().config, language },
      });

      await AsyncStorage.setItem(STORAGE_KEYS.LANGUAGE, language);
      await get().saveConfig();
    } catch (error) {
      console.error("Failed to set language:", error);
    }
  },

  // 设置网络状态
  setOnlineStatus: (isOnline: boolean) => {
    set({ isOnline });
  },

  // 更新配置
  updateConfig: async (newConfig: Partial<AppConfig>) => {
    try {
      const currentConfig = get().config;
      const updatedConfig = { ...currentConfig, ...newConfig };

      set({ config: updatedConfig });
      await get().saveConfig();
    } catch (error) {
      console.error("Failed to update config:", error);
    }
  },

  // 加载存储的配置
  loadStoredConfig: async () => {
    try {
      // 加载主题配置
      const themeConfigStr = await AsyncStorage.getItem(
        STORAGE_KEYS.THEME_CONFIG
      );
      if (themeConfigStr) {
        const themeConfig = JSON.parse(themeConfigStr);
        const theme = themeConfig.theme || "light";
        const actualTheme = getActualTheme(theme);
        set({
          theme,
          isDarkMode: actualTheme === "dark",
        });

        // 如果存储的主题是"跟随系统"，启动监听器
        if (theme === "system") {
          get().startSystemThemeListener();
        }
      } else {
        // 如果没有存储的配置，使用默认值
        const actualTheme = getActualTheme("light");
        set({
          theme: "light",
          isDarkMode: actualTheme === "dark",
        });
      }

      // 加载语言配置
      const language = (await AsyncStorage.getItem(
        STORAGE_KEYS.LANGUAGE
      )) as Language;
      if (language) {
        set({ language });
      }

      // 加载完整配置
      const configStr = await AsyncStorage.getItem("appConfig");
      if (configStr) {
        const config = JSON.parse(configStr);
        set({ config: { ...get().config, ...config } });
      }
    } catch (error) {
      console.error("Failed to load stored config:", error);
    }
  },

  // 保存配置
  saveConfig: async () => {
    try {
      const { config } = get();
      await AsyncStorage.setItem("appConfig", JSON.stringify(config));
    } catch (error) {
      console.error("Failed to save config:", error);
    }
  },

  // 初始化应用
  initializeApp: async () => {
    try {
      set({ isInitialized: false });

      // 加载存储的配置
      await get().loadStoredConfig();

      // 这里可以添加其他初始化逻辑
      // 比如检查应用更新、初始化第三方SDK等

      set({ isInitialized: true });
    } catch (error) {
      console.error("Failed to initialize app:", error);
      set({ isInitialized: true }); // 即使失败也要设置为已初始化
    }
  },

  // 处理系统主题变化 (带防抖的内部方法)
  _handleSystemThemeChangeInternal: (colorScheme: any) => {
    const { theme } = get();
    // 只在"跟随系统"模式下处理系统主题变化
    if (theme === "system") {
      const actualTheme = colorScheme === "dark" ? "dark" : "light";
      const currentIsDarkMode = get().isDarkMode;

      // 只有在主题实际发生变化时才更新状态，避免不必要的重新渲染
      if (currentIsDarkMode !== (actualTheme === "dark")) {
        set({ isDarkMode: actualTheme === "dark" });

        // 立即保存主题配置到存储
        AsyncStorage.setItem(
          STORAGE_KEYS.THEME_CONFIG,
          JSON.stringify({
            theme: "system",
            isDarkMode: actualTheme === "dark",
          })
        ).catch(error => {
          console.warn("Failed to save theme config after system change:", error);
        });
      }
    }
  },

  // 处理系统主题变化 (防抖版本)
  handleSystemThemeChange: (() => {
    // 创建防抖函数，100ms延迟以处理快速连续的主题切换
    const debouncedHandler = debounce((colorScheme: any) => {
      get()._handleSystemThemeChangeInternal(colorScheme);
    }, 100);

    return debouncedHandler;
  })(),

  // 开始监听系统主题变化
  startSystemThemeListener: () => {
    const { systemThemeListener, isSystemThemeListening } = get();

    // 如果已经在监听，则不重复注册
    if (isSystemThemeListening || systemThemeListener) {
      return;
    }

    try {
      if (isAppearanceAPIAvailable()) {
        // 立即同步当前系统主题状态
        const currentSystemTheme = getSystemTheme();
        const currentIsDarkMode = get().isDarkMode;
        const shouldBeDark = currentSystemTheme === "dark";
        
        if (currentIsDarkMode !== shouldBeDark) {
          set({ isDarkMode: shouldBeDark });
        }

        // 注册监听器以响应后续变化
        const listener = Appearance.addChangeListener(({ colorScheme }) => {
          // 立即处理主题变化，确保UI即时响应
          get().handleSystemThemeChange(colorScheme);
        });

        set({
          systemThemeListener: listener,
          isSystemThemeListening: true
        });
        
        console.log("System theme listener started successfully");
      } else {
        console.warn("Appearance API not available, system theme listening disabled");
      }
    } catch (error) {
      console.warn("Failed to start system theme listener:", error);
    }
  },

  // 停止监听系统主题变化
  stopSystemThemeListener: () => {
    const { systemThemeListener } = get();

    if (systemThemeListener) {
      try {
        if (systemThemeListener.remove) {
          systemThemeListener.remove();
        }
      } catch (error) {
        console.warn("Failed to remove system theme listener:", error);
      }

      set({
        systemThemeListener: null,
        isSystemThemeListening: false
      });
    }
  },
}));

// 导出主题相关的选择器
export const useTheme = () => {
  const { theme: themeMode, isDarkMode, setTheme, toggleTheme } = useAppStore();
  const { getTheme } = require("../components/design-system/theme");
  const theme = getTheme(isDarkMode);
  return { theme, themeMode, isDarkMode, setTheme, toggleTheme };
};

// 导出语言相关的选择器
export const useLanguage = () => {
  const { language, setLanguage } = useAppStore();
  return { language, setLanguage };
};

// 导出网络状态选择器
export const useNetworkStatus = () => {
  const { isOnline, setOnlineStatus } = useAppStore();
  return { isOnline, setOnlineStatus };
};
