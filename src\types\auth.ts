/**
 * 认证相关类型定义
 * HuiLink移动应用的认证数据结构
 */

// 登录请求参数
export interface LoginInput {
  account: string;
  password: string;
  code?: string;
  codeId?: number;
}

// 登录响应结果
export interface LoginOutput {
  accessToken: string;
  refreshToken?: string;
  expires?: number;
}

// 用户信息
export interface UserInfo {
  account: string;
  realName: string;
  avatar?: string;
  address?: string;
  signature?: string;
  orgId?: number;
  orgName?: string;
  posName?: string;
  roles: string[];
  authBtnList: string[];
  time: number;
}

// 验证码信息
export interface CaptchaInfo {
  id: number;
  img: string;
}

// 登录配置
export interface LoginConfig {
  captchaEnabled: boolean;
  secondVerEnabled: boolean;
}

// 水印配置
export interface WatermarkConfig {
  watermarkEnabled: boolean;
}

// API响应基础结构
export interface ApiResponse<T = any> {
  code: number;
  message: string;
  result: T;
  success: boolean;
  timestamp: number;
}

// 认证状态
export interface AuthState {
  isAuthenticated: boolean;
  user: UserInfo | null;
  token: string | null;
  isLoading: boolean;
  error: string | null;
}

// 登录表单状态
export interface LoginFormState {
  account: string;
  password: string;
  code: string;
  codeId: number;
  isShowPassword: boolean;
  captchaImage: string;
  captchaEnabled: boolean;
  secondVerEnabled: boolean;
  isLoading: boolean;
  error: string | null;
}

// JWT Token解析结果
export interface JWTPayload {
  exp: number;
  iat: number;
  nbf: number;
  sub: string;
  [key: string]: any;
}
