/**
 * Screen 组件
 * 屏幕级别的容器组件，提供安全区域和键盘处理
 */

import React from 'react';
import {
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  StatusBar,
  View,
  ViewStyle,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useTheme } from '../../stores/appStore';

interface ScreenProps {
  children: React.ReactNode;
  scrollable?: boolean;
  keyboardAvoiding?: boolean;
  backgroundColor?: string;
  statusBarStyle?: 'light-content' | 'dark-content' | 'default';
  statusBarBackgroundColor?: string;
  padding?: number | keyof typeof import('../design-system/tokens').Spacing;
  style?: ViewStyle;
  contentContainerStyle?: ViewStyle;
  testID?: string;
}

const Screen: React.FC<ScreenProps> = ({
  children,
  scrollable = false,
  keyboardAvoiding = true,
  backgroundColor,
  statusBarStyle,
  statusBarBackgroundColor,
  padding = 4,
  style,
  contentContainerStyle,
  testID,
}) => {
  const { theme } = useTheme();

  // 获取屏幕样式
  const getScreenStyle = (): ViewStyle => {
    return {
      flex: 1,
      backgroundColor: backgroundColor || theme.colors.background.primary,
    };
  };

  // 获取内容样式
  const getContentStyle = (): ViewStyle => {
    return {
      flex: 1,
      padding: typeof padding === 'number' ? padding : theme.spacing[padding as keyof typeof theme.spacing],
    };
  };

  // 获取状态栏样式
  const getStatusBarStyle = () => {
    if (statusBarStyle) {
      return statusBarStyle;
    }
    return theme.isDark ? 'light-content' : 'dark-content';
  };

  // 渲染内容
  const renderContent = () => {
    const content = (
      <View style={[getContentStyle(), contentContainerStyle]}>
        {children}
      </View>
    );

    if (scrollable) {
      return (
        <ScrollView
          style={{ flex: 1 }}
          contentContainerStyle={{ flexGrow: 1 }}
          keyboardShouldPersistTaps="handled"
          showsVerticalScrollIndicator={false}
        >
          {content}
        </ScrollView>
      );
    }

    return content;
  };

  // 渲染主容器
  const renderMainContainer = () => {
    const content = renderContent();

    if (keyboardAvoiding && Platform.OS === 'ios') {
      return (
        <KeyboardAvoidingView
          style={{ flex: 1 }}
          behavior="padding"
          keyboardVerticalOffset={0}
        >
          {content}
        </KeyboardAvoidingView>
      );
    }

    return content;
  };

  return (
    <SafeAreaView style={[getScreenStyle(), style]} testID={testID}>
      <StatusBar
        barStyle={getStatusBarStyle()}
        backgroundColor={statusBarBackgroundColor || theme.colors.background.primary}
        translucent={false}
      />
      {renderMainContainer()}
    </SafeAreaView>
  );
};

export default Screen;
