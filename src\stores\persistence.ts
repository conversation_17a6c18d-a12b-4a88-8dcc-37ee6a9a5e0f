/**
 * 状态持久化工具
 * 提供Zustand状态的本地存储和恢复功能
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import { StateCreator } from 'zustand';

// 持久化配置接口
export interface PersistConfig<T> {
  name: string; // 存储键名
  storage?: Storage; // 存储引擎，默认使用AsyncStorage
  partialize?: (state: T) => Partial<T>; // 选择需要持久化的状态部分
  onRehydrateStorage?: (state: T) => void; // 状态恢复后的回调
  version?: number; // 版本号，用于状态迁移
  migrate?: (persistedState: any, version: number) => T; // 状态迁移函数
}

// AsyncStorage适配器
const asyncStorageAdapter = {
  getItem: async (name: string): Promise<string | null> => {
    try {
      return await AsyncStorage.getItem(name);
    } catch (error) {
      console.error(`Failed to get item ${name} from AsyncStorage:`, error);
      return null;
    }
  },
  
  setItem: async (name: string, value: string): Promise<void> => {
    try {
      await AsyncStorage.setItem(name, value);
    } catch (error) {
      console.error(`Failed to set item ${name} to AsyncStorage:`, error);
    }
  },
  
  removeItem: async (name: string): Promise<void> => {
    try {
      await AsyncStorage.removeItem(name);
    } catch (error) {
      console.error(`Failed to remove item ${name} from AsyncStorage:`, error);
    }
  },
};

// 创建持久化中间件
export const persist = <T>(
  config: StateCreator<T>,
  options: PersistConfig<T>
): StateCreator<T> => {
  return (set, get, api) => {
    const {
      name,
      storage = asyncStorageAdapter,
      partialize,
      onRehydrateStorage,
      version = 0,
      migrate,
    } = options;

    // 创建原始状态
    const originalState = config(set, get, api);

    // 保存状态到存储
    const saveState = async (state: T) => {
      try {
        const stateToSave = partialize ? partialize(state) : state;
        const serializedState = JSON.stringify({
          state: stateToSave,
          version,
        });
        await storage.setItem(name, serializedState);
      } catch (error) {
        console.error(`Failed to save state to storage:`, error);
      }
    };

    // 从存储恢复状态
    const rehydrateState = async () => {
      try {
        const serializedState = await storage.getItem(name);
        
        if (!serializedState) {
          return originalState;
        }

        const { state: persistedState, version: persistedVersion } = JSON.parse(serializedState);

        // 处理版本迁移
        let finalState = persistedState;
        if (migrate && persistedVersion !== version) {
          finalState = migrate(persistedState, persistedVersion);
        }

        // 合并持久化状态和原始状态
        const mergedState = { ...originalState, ...finalState };

        // 调用恢复回调
        if (onRehydrateStorage) {
          onRehydrateStorage(mergedState);
        }

        return mergedState;
      } catch (error) {
        console.error(`Failed to rehydrate state from storage:`, error);
        return originalState;
      }
    };

    // 包装set函数以自动保存状态
    const wrappedSet: typeof set = (partial, replace) => {
      set(partial, replace);
      const newState = get();
      saveState(newState);
    };

    // 初始化时恢复状态
    rehydrateState().then((rehydratedState) => {
      set(rehydratedState as any, true);
    });

    return {
      ...originalState,
      // 添加持久化相关方法
      _persist: {
        clearStorage: () => storage.removeItem(name),
        rehydrate: rehydrateState,
        getStorageValue: () => storage.getItem(name),
      },
    } as T;
  };
};

// 便捷的持久化Hook
export const usePersistStore = <T>(
  storeName: string,
  initialState: T,
  options?: Partial<PersistConfig<T>>
) => {
  const defaultOptions: PersistConfig<T> = {
    name: storeName,
    storage: asyncStorageAdapter,
    version: 1,
    ...options,
  };

  return persist<T>(
    (set, get) => ({
      ...initialState,
      // 添加通用的重置方法
      reset: () => set(initialState, true),
    }),
    defaultOptions
  );
};

// 批量清除存储
export const clearAllPersistedStores = async (storeNames: string[]) => {
  try {
    await AsyncStorage.multiRemove(storeNames);
    console.log('All persisted stores cleared');
  } catch (error) {
    console.error('Failed to clear persisted stores:', error);
  }
};

// 获取所有存储的键
export const getAllStorageKeys = async (): Promise<string[]> => {
  try {
    return await AsyncStorage.getAllKeys();
  } catch (error) {
    console.error('Failed to get all storage keys:', error);
    return [];
  }
};

// 获取存储使用情况
export const getStorageInfo = async () => {
  try {
    const keys = await AsyncStorage.getAllKeys();
    const items = await AsyncStorage.multiGet(keys);
    
    let totalSize = 0;
    const itemSizes: Record<string, number> = {};
    
    items.forEach(([key, value]) => {
      const size = value ? new Blob([value]).size : 0;
      itemSizes[key] = size;
      totalSize += size;
    });
    
    return {
      totalKeys: keys.length,
      totalSize,
      itemSizes,
      keys,
    };
  } catch (error) {
    console.error('Failed to get storage info:', error);
    return null;
  }
};
