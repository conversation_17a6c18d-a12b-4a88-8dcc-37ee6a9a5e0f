/**
 * 数据平台WebView组件 - 简化版
 * 只保留核心功能：身份认证和WebView显示
 */

import React, { forwardRef, useCallback, useImperativeHandle, useRef, useState } from 'react';
import { StyleSheet, View } from 'react-native';
import { WebView } from 'react-native-webview';
import { CURRENT_FANRUAN_CONFIG } from '../constants/config';
import { useAuthStore } from '../stores';
import { useTheme } from '../stores/appStore';
import { FanruanAuthConfig, generateFanruanSSOUrl } from '../utils/fanruanAuth';

interface DataPlatformWebViewProps {
  onError?: (error: string) => void;
}

export interface DataPlatformWebViewRef {
  reload: () => void;
}

const DataPlatformWebView = forwardRef<DataPlatformWebViewRef, DataPlatformWebViewProps>(({
  onError,
}, ref) => {
  const { theme } = useTheme();
  const { user } = useAuthStore();
  const webViewRef = useRef<WebView>(null);
  
  const [ssoUrl, setSsoUrl] = useState<string | null>(null);

  // 生成单点登录URL
  const generateSSOUrl = useCallback(() => {
    if (!user?.account) {
      onError?.('用户信息不完整，无法生成登录链接');
      return;
    }

    const config: FanruanAuthConfig = {
      serverUrl: CURRENT_FANRUAN_CONFIG.SERVER_URL,
      projectName: CURRENT_FANRUAN_CONFIG.PROJECT_NAME,
      useEncryption: CURRENT_FANRUAN_CONFIG.USE_ENCRYPTION,
      publicKey: CURRENT_FANRUAN_CONFIG.PUBLIC_KEY,
      enableTimeout: CURRENT_FANRUAN_CONFIG.ENABLE_TIMEOUT,
    };

    const result = generateFanruanSSOUrl(config, user.account);

    if (result.success && result.url) {
      setSsoUrl(result.url);
    } else {
      onError?.(result.error || '生成登录链接失败');
    }
  }, [user?.account, onError]);

  // 初始化时生成SSO URL
  React.useEffect(() => {
    generateSSOUrl();
  }, [generateSSOUrl]);

  // 刷新页面
  const handleRefresh = useCallback(() => {
    if (webViewRef.current) {
      webViewRef.current.reload();
    }
  }, []);

  // 暴露给父组件的方法
  useImperativeHandle(ref, () => ({
    reload: handleRefresh,
  }), [handleRefresh]);

  if (!ssoUrl) {
    return <View style={styles.container} />;
  }

  // 渲染WebView
  return (
    <View style={[styles.container, { backgroundColor: theme.colors.background.secondary }]}>
      <WebView
        ref={webViewRef}
        source={{ uri: ssoUrl }}
        style={styles.webview}
        javaScriptEnabled={true}
        domStorageEnabled={true}
      />
    </View>
  );
});

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  webview: {
    flex: 1,
  },
});

export default DataPlatformWebView;
