# Tabs 导航实现文档

## 概述

本文档描述了如何在 HuiLink 移动应用中实现底部 Tab 导航，使用 expo-router 的 Tabs 组件。

## 文件结构

```
app/
├── _layout.tsx                 # 根布局，配置 Stack 导航
├── index.tsx                   # 应用入口，包含认证逻辑
├── (tabs)/                     # Tabs 路由组
│   ├── _layout.tsx            # Tabs 布局配置
│   ├── index.tsx              # 首页 Tab（引用 HomeScreen）
│   ├── work.tsx               # 工作 Tab
│   └── profile.tsx            # 我的 Tab
├── (dataplatform)/            # 数据平台路由组（现有）
└── (finance)/                 # 财务路由组（现有）

src/
├── components/
│   ├── AuthGuard.tsx          # 认证保护组件（已修改）
│   └── ...
├── screens/
│   ├── HomeScreen.tsx         # 原有首页组件
│   └── ...
└── ...
```

## Tab 配置

### 三个 Tab 页面

1. **首页 Tab** (`app/(tabs)/index.tsx`)
   - 图标：FontAwesome "home"
   - 标题：首页
   - 内容：引用现有的 `HomeScreen` 组件

2. **工作 Tab** (`app/(tabs)/work.tsx`)
   - 图标：FontAwesome "briefcase"
   - 标题：工作
   - 内容：工作台功能，包含预收款、收款报表、数据平台等模块

3. **我的 Tab** (`app/(tabs)/profile.tsx`)
   - 图标：FontAwesome "user"
   - 标题：我的
   - 内容：用户个人信息、设置选项、退出登录

### Tab 样式配置

- 激活状态颜色：`#007BFF`（蓝色）
- 非激活状态颜色：`#6B7280`（灰色）
- Tab 栏背景：白色
- Tab 栏高度：60px
- 图标大小：24px

## 认证流程

1. 用户打开应用，进入 `app/index.tsx`
2. `AppInitializer` 初始化应用状态
3. `AuthGuard` 检查用户认证状态：
   - 未认证：显示登录页面
   - 已认证：自动导航到 `/(tabs)` 路由

## 导航逻辑

- 用户登录成功后，`AuthGuard` 自动导航到 Tabs 页面
- 在 Tabs 内部可以导航到其他路由组（如 `/(dataplatform)`、`/(finance)`）
- 退出登录后返回登录页面

## 图标库

使用 `@expo/vector-icons` 中的 FontAwesome 图标：

```typescript
import FontAwesome from '@expo/vector-icons/FontAwesome';

// 使用示例
<FontAwesome size={24} name="home" color={color} />
```

## 自定义和扩展

### 添加新的 Tab

1. 在 `app/(tabs)/` 目录下创建新的页面文件
2. 在 `app/(tabs)/_layout.tsx` 中添加新的 `Tabs.Screen` 配置
3. 选择合适的图标和标题

### 修改 Tab 样式

在 `app/(tabs)/_layout.tsx` 的 `screenOptions` 中修改：

```typescript
screenOptions={{
  tabBarActiveTintColor: '#007BFF',      // 激活颜色
  tabBarInactiveTintColor: '#6B7280',    // 非激活颜色
  tabBarStyle: {
    backgroundColor: '#FFFFFF',          // 背景色
    height: 60,                         // 高度
    // ... 其他样式
  },
}}
```

### 隐藏特定 Tab

如果需要隐藏某个 Tab（但保留路由），可以设置 `href: null`：

```typescript
<Tabs.Screen
  name="hidden-tab"
  options={{
    href: null,  // 隐藏 Tab 按钮
  }}
/>
```

## 注意事项

1. **路由类型安全**：由于 TypeScript 可能还没有识别新的路由，在使用 `router.replace()` 时可能需要添加 `as any` 类型断言。

2. **现有功能兼容**：所有现有的路由组（`(dataplatform)`、`(finance)`）都保持不变，可以从 Tabs 内部正常访问。

3. **认证状态**：确保在修改认证逻辑时不会影响现有的登录/登出功能。

4. **性能考虑**：Tab 页面会在首次访问时加载，后续切换会保持状态。

## 测试建议

1. 测试登录后是否正确导航到 Tabs 页面
2. 测试各个 Tab 之间的切换
3. 测试从 Tab 页面导航到其他路由组
4. 测试退出登录功能
5. 测试应用重启后的状态恢复

## 故障排除

### 常见问题

1. **Tab 不显示**：检查 `app/(tabs)/_layout.tsx` 中的配置
2. **图标不显示**：确认 `@expo/vector-icons` 已正确安装
3. **导航失败**：检查路由路径是否正确
4. **样式问题**：检查 Tab 样式配置和主题设置
