/**
 * 主页屏幕
 * 登录后的主页面
 */

import { router } from 'expo-router';
import React from 'react';
import { Alert, StyleSheet, TouchableOpacity, View } from 'react-native';
import { Button, Card, Container, Screen, Text } from '../components/ui';
import SvgIcon from '../components/ui/SvgIcon';
import { useAuthStore } from '../stores';
import { useTheme } from '../stores/appStore';
import { formatTime } from '../utils';

const HomeScreen: React.FC = () => {
  const { theme } = useTheme();
  const { user, logout } = useAuthStore();

  const handleLogout = async () => {
    await logout();
  };

  const greeting = formatTime();

  // 功能模块数据
  const functionModules = [
    {
      id: 1,
      title: '预收款',
      iconName: 'prepay' as const,
      iconColor: '#28A745',
      route: '/(finance)/prepay',
      permission: 'home:collection',
    },
    {
      id: 2,
      title: '收款报表',
      iconName: 'prepayreport' as const,
      iconColor: '#007BFF',
      route: '/(finance)/prepayreport',
      permission: 'home:collection',
    },
    {
      id: 3,
      title: '数据平台',
      iconName: 'dataplatform' as const,
      iconColor: '#6F42C1',
      route: '/(dataplatform)',
      permission: 'home:dataplatform',
    },
  ];

  // 处理功能模块点击
  const handleModulePress = (module: typeof functionModules[0]) => {
    // TODO: 这里可以添加权限检查逻辑
    // if (!hasPermission(module.permission)) {
    //   Alert.alert('提示', '您没有访问此功能的权限');
    //   return;
    // }

    try {
      // 实现实际的路由跳转
      router.push(module.route as any);
    } catch (error) {
      console.error('路由跳转失败:', error);
      Alert.alert('跳转失败', '页面跳转出现错误，请稍后重试');
    }
  };

  return (
    <Screen backgroundColor={theme.colors.background.secondary}>
      <Container flex={1} padding={6}>
        {/* 欢迎区域 */}
        <Card padding={6} elevation={2} style={{ marginBottom: theme.spacing[4], minHeight: 100 }}>
          <Container>
            <Text variant="h3" weight="semibold" style={{ marginBottom: theme.spacing[2] }}>
              {greeting}
            </Text>

            <Text variant="h5" color="secondary">
              {user?.realName || user?.account || '用户'}
            </Text>
            {user?.orgName && (
              <Text variant="body2" color="secondary" style={{ marginTop: theme.spacing[1] }}>
                {user.orgName}
              </Text>
            )}
          </Container>
        </Card>

        {/* 功能模块区域 */}
        <Card padding={6} elevation={2} style={{ marginBottom: theme.spacing[4] }}>
          <Container>
            <Text variant="h6" weight="semibold" style={{ marginBottom: theme.spacing[4] }}>
              应用功能
            </Text>

            <View style={styles.moduleGrid}>
              {functionModules.map((module) => (
                <TouchableOpacity
                  key={module.id}
                  style={[styles.moduleItem, { backgroundColor: theme.colors.background.primary }]}
                  onPress={() => handleModulePress(module)}
                  activeOpacity={0.7}
                >
                  <View style={styles.moduleIconContainer}>
                    <SvgIcon
                      name={module.iconName}
                      size={32}
                      color={module.iconColor}
                    />
                  </View>
                  <Text variant="body1" style={styles.moduleText} numberOfLines={2}>
                    {module.title}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </Container>
        </Card>

        {/* 用户信息卡片 */}
        <Card padding={6} elevation={2} style={{ marginBottom: theme.spacing[4], minHeight: 200 }}>
          <Container>
            <Text variant="h6" weight="semibold" style={{ marginBottom: theme.spacing[4] }}>
              用户信息
            </Text>

            <View style={styles.infoRow}>
              <Text variant="body1" color="secondary" style={styles.label}>
                账号：
              </Text>
              <Text variant="body1">{user?.account}</Text>
            </View>

            <View style={styles.infoRow}>
              <Text variant="body1" color="secondary" style={styles.label}>
                姓名：
              </Text>
              <Text variant="body1">{user?.realName || '未设置'}</Text>
            </View>

            {user?.orgName && (
              <View style={styles.infoRow}>
                <Text variant="body1" color="secondary" style={styles.label}>
                  组织：
                </Text>
                <Text variant="body1">{user.orgName}</Text>
              </View>
            )}

            {user?.posName && (
              <View style={styles.infoRow}>
                <Text variant="body1" color="secondary" style={styles.label}>
                  职位：
                </Text>
                <Text variant="body1">{user.posName}</Text>
              </View>
            )}
          </Container>
        </Card>

        {/* 底部操作 */}
        <Container style={{ marginTop: 'auto' }}>
          <Button
            title="退出登录"
            variant="outline"
            onPress={handleLogout}
          />
        </Container>
      </Container>
    </Screen>
  );
};

const styles = StyleSheet.create({
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  label: {
    minWidth: 45, // 减少宽度以适应iPhone小屏幕
  },
  moduleGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    gap: 16,
  },
  moduleItem: {
    width: '48%', // 每行显示2个模块
    minHeight: 100,
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  moduleIconContainer: {
    width: 48,
    height: 48,
    marginBottom: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  moduleIcon: {
    width: 40,
    height: 40,
  },
  moduleText: {
    textAlign: 'center',
    fontSize: 12,
    lineHeight: 16,
  },
});

export default HomeScreen;
