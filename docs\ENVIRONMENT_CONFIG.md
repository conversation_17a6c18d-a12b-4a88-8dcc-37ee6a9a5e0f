# 环境配置指南

## 概述

HuiLink App 支持多环境自动切换，包括开发环境、测试环境和生产环境。

## 环境类型

### 1. 开发环境 (development)
- **触发条件**: `__DEV__ === true` (React Native 调试模式)
- **API地址**: `http://10.27.84.237:5006`
- **特点**: 启用调试日志、详细错误信息

### 2. 测试环境 (staging)
- **触发条件**: 设置环境变量 `EXPO_PUBLIC_ENV=staging`
- **API地址**: `https://staging-api.huilink.com`
- **特点**: 启用部分调试功能

### 3. 生产环境 (production)
- **触发条件**: 发布版本或设置 `NODE_ENV=production`
- **API地址**: `https://api.huilink.com`
- **特点**: 关闭调试功能，仅记录错误日志

## 配置方法

### 方法一：自动检测（推荐）
无需任何配置，应用会自动根据运行模式切换环境：
- 开发模式 (`expo start`) → 开发环境
- 发布版本 → 生产环境

### 方法二：环境变量配置
1. 复制 `.env.example` 为 `.env`
2. 设置环境变量：
```bash
# 开发环境
EXPO_PUBLIC_ENV=development

# 测试环境
EXPO_PUBLIC_ENV=staging

# 生产环境
EXPO_PUBLIC_ENV=production
```

## 使用示例

### 基础用法
```typescript
import { API_CONFIG, ENV_INFO, EnvUtils } from '@/src/constants/config';

// 获取当前环境的 API 地址
console.log(API_CONFIG.BASE_URL);

// 检查当前环境
if (ENV_INFO.isDevelopment) {
  console.log('当前是开发环境');
}

// 使用工具函数
EnvUtils.devOnly(() => {
  console.log('这段代码只在开发环境执行');
});
```

### 条件执行
```typescript
import { EnvUtils } from '@/src/constants/config';

// 仅在开发环境执行
EnvUtils.devOnly(() => {
  // 开发环境专用代码
  console.log('开发环境调试信息');
});

// 仅在生产环境执行
EnvUtils.prodOnly(() => {
  // 生产环境专用代码
  analytics.track('app_started');
});
```

### 环境日志
```typescript
import { EnvUtils } from '@/src/constants/config';

// 根据环境配置输出日志
EnvUtils.log('应用启动', 'info');
EnvUtils.log('调试信息', 'debug');
EnvUtils.log('警告信息', 'warn');
EnvUtils.log('错误信息', 'error');
```

## 注意事项

1. **环境变量命名**: 在 Expo 中，客户端可访问的环境变量必须以 `EXPO_PUBLIC_` 开头
2. **缓存清理**: 修改环境变量后，建议清理缓存：`expo start --clear`
3. **生产构建**: 使用 `expo build` 或 `eas build` 时会自动切换到生产环境
4. **安全性**: 不要在环境变量中存储敏感信息，如密钥或密码

## 故障排除

### 环境未正确切换
1. 检查环境变量是否正确设置
2. 清理缓存：`expo start --clear`
3. 重启开发服务器

### API 请求失败
1. 确认当前环境的 API 地址是否正确
2. 检查网络连接
3. 查看控制台日志获取详细错误信息
