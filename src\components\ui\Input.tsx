/**
 * Input 组件
 * 可定制的输入框组件，支持多种状态和验证
 */

import { Ionicons } from '@expo/vector-icons';
import React, { useState } from 'react';
import {
    Text,
    TextInput,
    TextStyle,
    TouchableOpacity,
    View,
    ViewStyle
} from 'react-native';
import { useTheme } from '../../stores/appStore';
import { InputProps } from '../../types';

const Input: React.FC<InputProps> = ({
  value,
  onChangeText,
  placeholder,
  secureTextEntry = false,
  keyboardType = 'default',
  autoCapitalize = 'none',
  autoCorrect = false,
  editable = true,
  maxLength,
  multiline = false,
  numberOfLines = 1,
  error,
  label,
  required = false,
  style,
  testID,
}) => {
  const { theme } = useTheme();
  const [isFocused, setIsFocused] = useState(false);
  const [isPasswordVisible, setIsPasswordVisible] = useState(false);

  // 获取容器样式
  const getContainerStyle = (): ViewStyle => {
    return {
      marginBottom: theme.spacing[4],
    };
  };

  // 获取标签样式
  const getLabelStyle = (): TextStyle => {
    return {
      fontSize: theme.typography.fontSize.sm,
      fontWeight: theme.typography.fontWeight.medium,
      color: theme.colors.text.primary,
      marginBottom: theme.spacing[1],
    };
  };

  // 获取输入框容器样式
  const getInputContainerStyle = (): ViewStyle => {
    const baseStyle: ViewStyle = {
      flexDirection: 'row',
      alignItems: 'center',
      borderWidth: 1,
      borderRadius: theme.borderRadius.md,
      paddingHorizontal: theme.spacing[4],
      backgroundColor: theme.colors.surface.primary,
      minHeight: multiline ? 80 : 56,
      transition: 'all 0.2s ease-in-out',
    };

    // 状态样式
    if (error) {
      return {
        ...baseStyle,
        borderColor: theme.colors.error,
        backgroundColor: theme.colors.surface.primary,
        shadowColor: theme.colors.error,
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
        elevation: 2,
      };
    }

    if (isFocused) {
      return {
        ...baseStyle,
        borderColor: theme.colors.primary,
        backgroundColor: theme.colors.surface.primary,
        shadowColor: theme.colors.primary,
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.15,
        shadowRadius: 12,
        elevation: 4,
        transform: [{ scale: 1.02 }],
      };
    }

    if (!editable) {
      return {
        ...baseStyle,
        borderColor: theme.colors.border.secondary,
        backgroundColor: theme.colors.surface.tertiary,
      };
    }

    return {
      ...baseStyle,
      borderColor: theme.colors.border.lighter,
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.03,
      shadowRadius: 2,
      elevation: 1,
    };
  };

  // 获取输入框样式
  const getInputStyle = (): TextStyle => {
    return {
      flex: 1,
      fontSize: theme.typography.fontSize.base,
      fontFamily: theme.typography.fontFamily.primary,
      color: editable ? theme.colors.text.primary : theme.colors.text.disabled,
      paddingVertical: theme.spacing[2],
      textAlignVertical: multiline ? 'top' : 'center',
    };
  };

  // 获取错误文本样式
  const getErrorStyle = (): TextStyle => {
    return {
      fontSize: theme.typography.fontSize.sm,
      color: theme.colors.error,
      marginTop: theme.spacing[1],
    };
  };

  // 获取必填标记样式
  const getRequiredStyle = (): TextStyle => {
    return {
      color: theme.colors.error,
      fontSize: theme.typography.fontSize.sm,
    };
  };

  const togglePasswordVisibility = () => {
    setIsPasswordVisible(!isPasswordVisible);
  };

  const handleFocus = () => {
    setIsFocused(true);
  };

  const handleBlur = () => {
    setIsFocused(false);
  };

  return (
    <View style={[getContainerStyle(), style]}>
      {/* 标签 */}
      {label && (
        <View style={{ flexDirection: 'row', alignItems: 'center' }}>
          <Text style={getLabelStyle()}>{label}</Text>
          {required && <Text style={getRequiredStyle()}> *</Text>}
        </View>
      )}

      {/* 输入框容器 */}
      <View style={getInputContainerStyle()}>
        <TextInput
          style={getInputStyle()}
          value={value}
          onChangeText={onChangeText}
          placeholder={placeholder}
          placeholderTextColor={theme.colors.text.tertiary}
          secureTextEntry={secureTextEntry && !isPasswordVisible}
          keyboardType={keyboardType}
          autoCapitalize={autoCapitalize}
          autoCorrect={autoCorrect}
          editable={editable}
          maxLength={maxLength}
          multiline={multiline}
          numberOfLines={numberOfLines}
          onFocus={handleFocus}
          onBlur={handleBlur}
          testID={testID}
        />

        {/* 密码可见性切换按钮 */}
        {secureTextEntry && (
          <TouchableOpacity
            onPress={togglePasswordVisibility}
            style={{
              padding: theme.spacing[1],
              marginLeft: theme.spacing[2],
            }}
          >
            <Ionicons
              name={isPasswordVisible ? 'eye-off' : 'eye'}
              size={20}
              color={theme.colors.text.secondary}
            />
          </TouchableOpacity>
        )}
      </View>

      {/* 错误信息 */}
      {error && <Text style={getErrorStyle()}>{error}</Text>}
    </View>
  );
};

export default Input;
