/**
 * 错误处理工具
 * 提供统一的错误处理和用户反馈机制
 */

import { AxiosError } from 'axios';
import { HTTP_STATUS, ERROR_MESSAGES } from '../constants';
import { ErrorInfo } from '../types';
import { showError } from './toast';

/**
 * 错误类型枚举
 */
export enum ErrorType {
  NETWORK_ERROR = 'NETWORK_ERROR',
  AUTH_ERROR = 'AUTH_ERROR',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  BUSINESS_ERROR = 'BUSINESS_ERROR',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR',
}

/**
 * 自定义错误类
 */
export class AppError extends Error {
  public type: ErrorType;
  public code: string | number;
  public details?: any;
  public timestamp: number;

  constructor(
    message: string,
    type: ErrorType = ErrorType.UNKNOWN_ERROR,
    code: string | number = 'UNKNOWN',
    details?: any
  ) {
    super(message);
    this.name = 'AppError';
    this.type = type;
    this.code = code;
    this.details = details;
    this.timestamp = Date.now();
  }

  toErrorInfo(): ErrorInfo {
    return {
      code: this.code,
      message: this.message,
      details: this.details,
      timestamp: this.timestamp,
    };
  }
}

/**
 * 错误处理器类
 */
export class ErrorHandler {
  /**
   * 处理Axios错误
   */
  static handleAxiosError(error: AxiosError): AppError {
    if (error.response) {
      // 服务器响应错误
      const { status, data } = error.response;
      
      switch (status) {
        case HTTP_STATUS.UNAUTHORIZED:
          return new AppError(
            ERROR_MESSAGES.TOKEN_EXPIRED,
            ErrorType.AUTH_ERROR,
            status,
            data
          );
        
        case HTTP_STATUS.FORBIDDEN:
          return new AppError(
            '没有权限访问该资源',
            ErrorType.AUTH_ERROR,
            status,
            data
          );
        
        case HTTP_STATUS.NOT_FOUND:
          return new AppError(
            '请求的资源不存在',
            ErrorType.BUSINESS_ERROR,
            status,
            data
          );
        
        case HTTP_STATUS.INTERNAL_SERVER_ERROR:
          return new AppError(
            '服务器内部错误',
            ErrorType.BUSINESS_ERROR,
            status,
            data
          );
        
        default:
          return new AppError(
            (data as any)?.message || error.message || '请求失败',
            ErrorType.BUSINESS_ERROR,
            status,
            data
          );
      }
    } else if (error.request) {
      // 网络错误
      return new AppError(
        ERROR_MESSAGES.NETWORK_ERROR,
        ErrorType.NETWORK_ERROR,
        'NETWORK_ERROR',
        error.request
      );
    } else {
      // 请求配置错误
      return new AppError(
        error.message || ERROR_MESSAGES.UNKNOWN_ERROR,
        ErrorType.UNKNOWN_ERROR,
        'CONFIG_ERROR',
        error.config
      );
    }
  }

  /**
   * 处理业务逻辑错误
   */
  static handleBusinessError(message: string, code?: string | number): AppError {
    return new AppError(
      message,
      ErrorType.BUSINESS_ERROR,
      code || 'BUSINESS_ERROR'
    );
  }

  /**
   * 处理验证错误
   */
  static handleValidationError(message: string, field?: string): AppError {
    return new AppError(
      message,
      ErrorType.VALIDATION_ERROR,
      'VALIDATION_ERROR',
      { field }
    );
  }

  /**
   * 处理认证错误
   */
  static handleAuthError(message: string): AppError {
    return new AppError(
      message,
      ErrorType.AUTH_ERROR,
      'AUTH_ERROR'
    );
  }

  /**
   * 统一错误处理入口
   */
  static handle(error: any, showToast: boolean = true): AppError {
    let appError: AppError;

    if (error instanceof AppError) {
      appError = error;
    } else if (error.isAxiosError) {
      appError = this.handleAxiosError(error as AxiosError);
    } else if (error instanceof Error) {
      appError = new AppError(
        error.message,
        ErrorType.UNKNOWN_ERROR,
        'UNKNOWN_ERROR'
      );
    } else if (typeof error === 'string') {
      appError = new AppError(
        error,
        ErrorType.UNKNOWN_ERROR,
        'UNKNOWN_ERROR'
      );
    } else {
      appError = new AppError(
        ERROR_MESSAGES.UNKNOWN_ERROR,
        ErrorType.UNKNOWN_ERROR,
        'UNKNOWN_ERROR',
        error
      );
    }

    // 记录错误日志
    this.logError(appError);

    // 显示用户提示
    if (showToast) {
      this.showErrorToast(appError);
    }

    return appError;
  }

  /**
   * 记录错误日志
   */
  static logError(error: AppError): void {
    console.error('Error occurred:', {
      type: error.type,
      code: error.code,
      message: error.message,
      details: error.details,
      timestamp: new Date(error.timestamp).toISOString(),
      stack: error.stack,
    });

    // 这里可以集成错误监控服务，如Sentry
    // Sentry.captureException(error);
  }

  /**
   * 显示错误提示
   */
  static showErrorToast(error: AppError): void {
    // 根据错误类型显示不同的提示
    switch (error.type) {
      case ErrorType.NETWORK_ERROR:
        showError(ERROR_MESSAGES.NETWORK_ERROR);
        break;
      
      case ErrorType.AUTH_ERROR:
        showError(error.message);
        break;
      
      case ErrorType.VALIDATION_ERROR:
        showError(error.message);
        break;
      
      case ErrorType.BUSINESS_ERROR:
        showError(error.message);
        break;
      
      default:
        showError(ERROR_MESSAGES.UNKNOWN_ERROR);
        break;
    }
  }
}

/**
 * Promise错误包装器
 * 与Vue项目的feature函数保持一致
 */
export async function safeAsync<T>(
  promise: Promise<T>
): Promise<[AppError | null, T | null]> {
  try {
    const result = await promise;
    return [null, result];
  } catch (error) {
    const appError = ErrorHandler.handle(error, false);
    return [appError, null];
  }
}
