/**
 * 登录屏幕
 * HuiLink移动应用的用户登录界面 - 现代化设计
 */

import { Box } from '@/components/ui/box';
import { Button, ButtonText } from '@/components/ui/button';
import { Input, InputField, InputSlot } from '@/components/ui/input';
import { Text } from '@/components/ui/text';
import { LinearGradient } from 'expo-linear-gradient';
import React, { useEffect, useRef, useState } from 'react';
import {
  Image,
  KeyboardAvoidingView,
  Platform,
  ScrollView,
  StatusBar,
  StyleSheet,
  TouchableOpacity
} from 'react-native';
import { SafeAreaView, useSafeAreaInsets } from 'react-native-safe-area-context';
import { ERROR_MESSAGES, VALIDATION_CONFIG } from '../constants';
import { useAuthStore } from '../stores';

// 现代化设计主题 - 玻璃拟态风格
const theme = {
  colors: {
    primary: '#0ea5e9',
    primaryGradient: ['#0ea5e9', '#0284c7', '#0369a1'],
    backgroundGradient: ['#f0f9ff', '#e0f2fe', '#bae6fd', '#0ea5e9'],
    surface: 'rgba(255, 255, 255, 0.9)',
    surfaceGradient: ['rgba(255, 255, 255, 0.9)', 'rgba(248, 250, 252, 0.95)', 'rgba(241, 245, 249, 0.9)'],
    inputBackground: ['rgba(255, 255, 255, 0.8)', 'rgba(248, 250, 252, 0.9)'],
    inputFocusBackground: 'rgba(255, 255, 255, 0.95)',
    text: {
      primary: '#1e293b',
      secondary: '#64748b',
      tertiary: '#374151',
    },
    border: 'rgba(209, 213, 219, 0.6)',
    borderFocus: 'rgba(14, 165, 233, 0.8)',
    error: '#ef4444',
    errorBackground: '#fef2f2',
  },
  spacing: {
    xs: 4,
    sm: 8,
    md: 16,
    lg: 24,
    xl: 32,
    xxl: 48,
  },
  borderRadius: {
    sm: 12,
    md: 16,
    lg: 20,
    xl: 24,
  },
  shadows: {
    card: {
      shadowColor: '#0ea5e9',
      shadowOffset: { width: 0, height: 12 },
      shadowOpacity: 0.08,
      shadowRadius: 24,
      elevation: 12,
    },
    cardInner: {
      shadowColor: '#0ea5e9',
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.05,
      shadowRadius: 8,
      elevation: 4,
    },
    button: {
      shadowColor: '#0ea5e9',
      shadowOffset: { width: 0, height: 6 },
      shadowOpacity: 0.25,
      shadowRadius: 16,
      elevation: 8,
    },
    input: {
      shadowColor: '#0ea5e9',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.04,
      shadowRadius: 6,
      elevation: 2,
    },
    inputFocus: {
      shadowColor: '#0ea5e9',
      shadowOffset: { width: 0, height: 0 },
      shadowOpacity: 0.08,
      shadowRadius: 8,
      elevation: 3,
    },
  },
};

// 自定义输入框组件
const CustomInput = ({
  label,
  value,
  onChangeText,
  placeholder,
  secureTextEntry = false,
  error,
  fieldName,
  rightIcon,
  focusedField,
  setFocusedField,
  keyboardType = 'default',
  autoComplete = 'off',
  textContentType,
  ...otherProps
}: any) => {
  const isFocused = focusedField === fieldName;

  return (
    <Box style={styles.inputContainer}>
      <Text style={styles.inputLabel}>{label}</Text>
      <Input
        variant="outline"
        size="md"
        style={[
          styles.inputWrapper,
          isFocused && styles.inputWrapperFocused,
          error && styles.inputWrapperError
        ]}
        isInvalid={!!error}
        isFocused={isFocused}
      >
        <InputField
          style={styles.input}
          value={value}
          onChangeText={onChangeText}
          placeholder={placeholder}
          placeholderTextColor={theme.colors.text.secondary}
          secureTextEntry={secureTextEntry}
          onFocus={() => setFocusedField(fieldName)}
          onBlur={() => setFocusedField(null)}
          autoCapitalize="none"
          autoCorrect={false}
          keyboardType={keyboardType}
          autoComplete={autoComplete}
          textContentType={textContentType}
          {...otherProps}
        />
        {rightIcon && (
          <InputSlot style={styles.inputRightIcon}>
            {rightIcon}
          </InputSlot>
        )}
      </Input>
      <Box style={styles.errorTextContainer}>
        {error ? <Text style={styles.errorText}>{error}</Text> : null}
      </Box>
    </Box>
  );
};

const LoginScreen: React.FC = () => {
  // 获取安全区域信息
  const insets = useSafeAreaInsets();

  // ScrollView引用，用于键盘处理
  const scrollViewRef = useRef<ScrollView>(null);

  const {
    login,
    getLoginConfig,
    getCaptcha,
    loginConfig,
    captchaInfo,
    isLoading,
    error,
    clearError,
  } = useAuthStore();

  // 表单状态
  const [formData, setFormData] = useState({
    account: '',
    password: '',
    code: '',
  });

  // 表单验证错误
  const [formErrors, setFormErrors] = useState({
    account: '',
    password: '',
    code: '',
  });

  // 密码可见性
  const [showPassword, setShowPassword] = useState(false);

  // 输入框焦点状态
  const [focusedField, setFocusedField] = useState<string | null>(null);

  // 组件挂载时获取配置
  useEffect(() => {
    getLoginConfig();
  }, []);

  // 当需要验证码时获取验证码
  useEffect(() => {
    if (loginConfig?.captchaEnabled) {
      getCaptcha();
    }
  }, [loginConfig]);

  // 清除错误信息
  useEffect(() => {
    if (error) {
      const timer = setTimeout(() => {
        clearError();
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [error]);

  // 表单验证
  const validateForm = (): boolean => {
    const errors = {
      account: '',
      password: '',
      code: '',
    };

    // 验证账号
    if (!formData.account.trim()) {
      errors.account = ERROR_MESSAGES.ACCOUNT_REQUIRED;
    } else if (!VALIDATION_CONFIG.ACCOUNT.PATTERN.test(formData.account)) {
      errors.account = ERROR_MESSAGES.ACCOUNT_FORMAT_ERROR;
    }

    // 验证密码
    if (!formData.password.trim()) {
      errors.password = ERROR_MESSAGES.PASSWORD_REQUIRED;
    } else if (
      formData.password.length < VALIDATION_CONFIG.PASSWORD.MIN_LENGTH ||
      formData.password.length > VALIDATION_CONFIG.PASSWORD.MAX_LENGTH
    ) {
      errors.password = ERROR_MESSAGES.PASSWORD_FORMAT_ERROR;
    }

    // 验证验证码（如果启用）
    if (loginConfig?.captchaEnabled && !formData.code.trim()) {
      errors.code = ERROR_MESSAGES.CAPTCHA_REQUIRED;
    }

    setFormErrors(errors);
    return !Object.values(errors).some(error => error !== '');
  };

  // 处理输入变化
  const handleInputChange = (field: keyof typeof formData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));

    // 清除对应字段的错误
    if (formErrors[field]) {
      setFormErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  // 处理登录
  const handleLogin = async () => {
    if (!validateForm()) {
      return;
    }

    const loginData = {
      account: formData.account.trim(),
      password: formData.password,
      code: formData.code.trim(),
      codeId: captchaInfo?.id,
    };

    const success = await login(loginData);

    if (success) {
      console.log('Login successful');
    } else {
      // 如果需要验证码，刷新验证码
      if (loginConfig?.captchaEnabled) {
        getCaptcha();
        setFormData(prev => ({ ...prev, code: '' }));
      }
    }
  };

  // 刷新验证码
  const handleRefreshCaptcha = () => {
    getCaptcha();
    setFormData(prev => ({ ...prev, code: '' }));
  };

  return (
    <SafeAreaView style={styles.safeArea} edges={['top']}>
      <StatusBar barStyle="dark-content" backgroundColor="transparent" translucent />
      <KeyboardAvoidingView
        style={styles.container}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 20}
      >
        <LinearGradient
          colors={['#f0f9ff', '#e0f2fe', '#bae6fd', '#0ea5e9']}
          style={styles.gradient}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        >
          <ScrollView
            ref={scrollViewRef}
            contentContainerStyle={[
              styles.scrollContent,
              { paddingTop: Math.max(insets.top, theme.spacing.md) }
            ]}
            showsVerticalScrollIndicator={false}
            keyboardShouldPersistTaps="handled"
          >
            {/* Logo 区域卡片 */}
            <Box style={styles.logoCard}>
              <Box style={styles.logoContainer}>
                <Image
                  source={require('@/assets/images/logos/login.png')}
                  style={styles.logoImageDirect}
                  resizeMode="contain"
                />
                <Text style={styles.appTitle}>智汇助手</Text>
                <Text style={styles.appSubtitle}>请登录您的账户</Text>
              </Box>
            </Box>

            {/* 登录表单卡片 */}
            <Box style={styles.formCard}>

              {/* 错误提示 */}
              {error && (
                <Box style={styles.errorContainer}>
                  <Text style={styles.errorMessage}>⚠️ {error}</Text>
                </Box>
              )}

              {/* 用户名输入框 */}
              <CustomInput
                label="用户名"
                value={formData.account}
                onChangeText={(value: string) => handleInputChange('account', value)}
                placeholder="请输入用户名"
                error={formErrors.account}
                fieldName="account"
                focusedField={focusedField}
                setFocusedField={setFocusedField}
                keyboardType="default"
                autoComplete="username"
                textContentType="username"
                returnKeyType="next"
              />

              {/* 密码输入框 */}
              <CustomInput
                label="密码"
                value={formData.password}
                onChangeText={(value: string) => handleInputChange('password', value)}
                placeholder="请输入密码"
                secureTextEntry={!showPassword}
                error={formErrors.password}
                fieldName="password"
                focusedField={focusedField}
                setFocusedField={setFocusedField}
                keyboardType="default"
                autoComplete="current-password"
                textContentType="password"
                returnKeyType={loginConfig?.captchaEnabled ? "next" : "done"}
                rightIcon={
                  <Button variant="link" size="sm" onPress={() => setShowPassword(!showPassword)}>
                    <ButtonText style={styles.passwordToggle}>
                      {showPassword ? '隐藏' : '显示'}
                    </ButtonText>
                  </Button>
                }
              />

              {/* 验证码输入框（条件显示） */}
              {loginConfig?.captchaEnabled && (
                <>
                  <CustomInput
                    label="验证码"
                    value={formData.code}
                    onChangeText={(value: string) => handleInputChange('code', value)}
                    placeholder="请输入验证码"
                    error={formErrors.code}
                    fieldName="code"
                    focusedField={focusedField}
                    setFocusedField={setFocusedField}
                    keyboardType="default"
                    autoComplete="one-time-code"
                    textContentType="oneTimeCode"
                    returnKeyType="done"
                    maxLength={6}
                  />

                  {/* 验证码图片 */}
                  {captchaInfo && (
                    <Box style={styles.captchaContainer}>
                      <Box style={styles.captchaImageWrapper}>
                        <Image
                          source={{ uri: `data:image/png;base64,${captchaInfo.img}` }}
                          style={styles.captchaImage}
                          resizeMode="contain"
                        />
                      </Box>
                      <Button
                        variant="outline"
                        size="sm"
                        style={styles.refreshButton}
                        onPress={handleRefreshCaptcha}
                      >
                        <ButtonText style={styles.refreshButtonText}>🔄 刷新</ButtonText>
                      </Button>
                    </Box>
                  )}
                </>
              )}

              {/* 登录按钮 */}
              <TouchableOpacity
                onPress={handleLogin}
                disabled={isLoading || !formData.account || !formData.password || (!!captchaInfo && !formData.code)}
                style={[
                  styles.loginButtonContainer,
                  (isLoading || !formData.account || !formData.password || (!!captchaInfo && !formData.code)) && styles.loginButtonDisabled
                ]}
              >
                <LinearGradient
                  colors={['#0ea5e9', '#0284c7', '#0369a1']}
                  start={{ x: 0, y: 0 }}
                  end={{ x: 1, y: 0 }}
                  style={styles.loginButton}
                >
                  <Text style={styles.loginButtonText}>
                    {isLoading ? '登录中...' : '登录'}
                  </Text>
                </LinearGradient>
              </TouchableOpacity>
            </Box>
          </ScrollView>
        </LinearGradient>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: '#f0f9ff',
  },
  container: {
    flex: 1,
  },
  gradient: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    justifyContent: 'center',
    padding: theme.spacing.md,
    minHeight: '100%',
  },
  logoCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    borderRadius: theme.borderRadius.xl,
    padding: theme.spacing.xl,
    marginHorizontal: theme.spacing.sm,
    marginBottom: theme.spacing.md,
    borderWidth: 0,
    borderColor: 'rgba(255, 255, 255, 0.4)',
    ...theme.shadows.card,
  },
  logoContainer: {
    alignItems: 'center',
  },

  logoImageDirect: {
    width: 280,
    height: 118,
    marginBottom: theme.spacing.lg,
  },
  appTitle: {
    fontSize: 28,
    fontWeight: '700',
    color: theme.colors.text.primary,
    marginBottom: theme.spacing.xs,
    marginTop: theme.spacing.md,
    lineHeight: 36,
    textAlign: 'center',
  },
  appSubtitle: {
    fontSize: 16,
    color: theme.colors.text.secondary,
    fontWeight: '400',
    lineHeight: 22,
    textAlign: 'center',
  },
  formCard: {
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    borderRadius: theme.borderRadius.xl,
    padding: theme.spacing.xxl,
    marginHorizontal: theme.spacing.sm,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.3)',
    // Web 兼容的阴影
    boxShadow: '0px 12px 24px rgba(14, 165, 233, 0.08)',
    // React Native 阴影（移动端）
    shadowColor: '#0ea5e9',
    shadowOffset: { width: 0, height: 12 },
    shadowOpacity: 0.08,
    shadowRadius: 24,
    elevation: 12,
  },
  errorContainer: {
    backgroundColor: theme.colors.errorBackground,
    borderColor: theme.colors.error + '30',
    borderWidth: 1,
    borderRadius: theme.borderRadius.sm,
    padding: theme.spacing.md,
    marginBottom: theme.spacing.lg,
  },
  errorMessage: {
    color: theme.colors.error,
    fontSize: 14,
    fontWeight: '500',
  },
  inputContainer: {
    marginBottom: theme.spacing.md,
    minHeight: 82, // 固定高度：标签(20px) + 输入框(52px) + 错误区域(18px) + 间距
  },
  inputLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: theme.colors.text.primary,
    marginBottom: 8,
    letterSpacing: 0.2,
    height: 20, // 固定标签高度
    lineHeight: 20,
  },
  inputWrapper: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: 'rgba(209, 213, 219, 0.6)',
    borderRadius: 12,
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    paddingHorizontal: 14,
    height: 52,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.06,
    shadowRadius: 4,
    elevation: 2,
  },
  inputWrapperFocused: {
    borderColor: 'rgba(14, 165, 233, 0.8)',
    backgroundColor: 'rgba(255, 255, 255, 0.95)',
    shadowColor: 'rgba(14, 165, 233, 0.3)',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 4,
    transform: [{ scale: 1.01 }],
  },
  inputWrapperError: {
    borderColor: theme.colors.error,
    backgroundColor: 'rgba(255, 245, 245, 0.9)',
    shadowColor: theme.colors.error,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  input: {
    flex: 1,
    fontSize: 16,
    color: theme.colors.text.primary,
    paddingVertical: 14,
    fontWeight: '400',
    lineHeight: 20,
  },
  inputRightIcon: {
    padding: theme.spacing.xs,
  },
  passwordToggle: {
    fontSize: 14,
    color: 'rgba(14, 165, 233, 0.8)',
    fontWeight: '500',
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
  errorTextContainer: {
    height: 18, // 固定高度，确保布局稳定
    marginTop: 6,
    justifyContent: 'flex-start',
  },
  errorText: {
    fontSize: 12,
    color: theme.colors.error,
    fontWeight: '500',
    marginLeft: 4,
    lineHeight: 16,
  },
  captchaContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginTop: theme.spacing.md,
    marginBottom: theme.spacing.lg,
    padding: theme.spacing.md,
    backgroundColor: 'rgba(255, 255, 255, 0.5)',
    borderRadius: theme.borderRadius.sm,
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.25)',
    // Web 兼容的阴影
    boxShadow: '0px 2px 6px rgba(14, 165, 233, 0.04)',
    // React Native 阴影（移动端）
    shadowColor: '#0ea5e9',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.04,
    shadowRadius: 6,
    elevation: 2,
  },
  captchaImageWrapper: {
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    borderRadius: theme.borderRadius.sm,
    // Web 兼容的阴影
    boxShadow: '0px 4px 8px rgba(14, 165, 233, 0.05)',
    // React Native 阴影（移动端）
    shadowColor: '#0ea5e9',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 4,
  },
  captchaImage: {
    width: 120,
    height: 40,
    borderRadius: theme.borderRadius.sm,
  },
  refreshButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.7)',
    borderWidth: 1,
    borderColor: 'rgba(255, 255, 255, 0.3)',
    borderRadius: theme.borderRadius.sm,
    paddingHorizontal: theme.spacing.md,
    paddingVertical: theme.spacing.sm,
    marginLeft: theme.spacing.md,
    // Web 兼容的阴影
    boxShadow: '0px 2px 6px rgba(14, 165, 233, 0.04)',
    // React Native 阴影（移动端）
    shadowColor: '#0ea5e9',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.04,
    shadowRadius: 6,
    elevation: 2,
  },
  refreshButtonText: {
    fontSize: 14,
    color: theme.colors.text.primary,
    fontWeight: '500',
  },
  loginButtonContainer: {
    marginTop: theme.spacing.xl,
    borderRadius: theme.borderRadius.sm,
    // Web 兼容的阴影
    boxShadow: '0px 6px 16px rgba(14, 165, 233, 0.25)',
    // React Native 阴影（移动端）
    shadowColor: '#0ea5e9',
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.25,
    shadowRadius: 16,
    elevation: 8,
  },
  loginButton: {
    borderRadius: theme.borderRadius.sm,
    paddingVertical: 16,
    paddingHorizontal: 24,
    alignItems: 'center',
    justifyContent: 'center',
    height: 52,
  },
  loginButtonDisabled: {
    opacity: 0.6,
  },
  loginButtonText: {
    color: 'white',
    fontSize: 16,
    fontWeight: '600',
  },
});

export default LoginScreen;
