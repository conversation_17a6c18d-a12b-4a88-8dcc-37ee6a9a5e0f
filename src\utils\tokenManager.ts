/**
 * Token管理工具
 * HuiLink移动应用的JWT令牌管理
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import { TOKEN_CONFIG } from '../constants';
import { JWTPayload } from '../types';

export class TokenManager {
  /**
   * 存储访问令牌
   */
  static async setToken(token: string): Promise<void> {
    try {
      await AsyncStorage.setItem(TOKEN_CONFIG.ACCESS_TOKEN_KEY, token);
      await AsyncStorage.setItem(TOKEN_CONFIG.STORAGE_TOKEN_KEY, token);
    } catch (error) {
      console.error('Failed to store token:', error);
      throw new Error('Token storage failed');
    }
  }

  /**
   * 获取访问令牌
   */
  static async getToken(): Promise<string | null> {
    try {
      return await AsyncStorage.getItem(TOKEN_CONFIG.ACCESS_TOKEN_KEY);
    } catch (error) {
      console.error('Failed to get token:', error);
      return null;
    }
  }

  /**
   * 存储刷新令牌
   */
  static async setRefreshToken(refreshToken: string): Promise<void> {
    try {
      await AsyncStorage.setItem(TOKEN_CONFIG.REFRESH_TOKEN_KEY, refreshToken);
    } catch (error) {
      console.error('Failed to store refresh token:', error);
      throw new Error('Refresh token storage failed');
    }
  }

  /**
   * 获取刷新令牌
   */
  static async getRefreshToken(): Promise<string | null> {
    try {
      return await AsyncStorage.getItem(TOKEN_CONFIG.REFRESH_TOKEN_KEY);
    } catch (error) {
      console.error('Failed to get refresh token:', error);
      return null;
    }
  }

  /**
   * 清除所有令牌
   */
  static async clearTokens(): Promise<void> {
    try {
      await AsyncStorage.multiRemove([
        TOKEN_CONFIG.ACCESS_TOKEN_KEY,
        TOKEN_CONFIG.REFRESH_TOKEN_KEY,
        TOKEN_CONFIG.STORAGE_TOKEN_KEY,
        TOKEN_CONFIG.STORAGE_USER_INFO_KEY,
      ]);
    } catch (error) {
      console.error('Failed to clear tokens:', error);
      throw new Error('Token clearing failed');
    }
  }

  /**
   * 检查令牌是否存在
   */
  static async hasToken(): Promise<boolean> {
    const token = await this.getToken();
    return !!token;
  }

  /**
   * 解密JWT令牌
   * 解析JWT令牌获取载荷信息
   */
  static decryptJWT(token: string): JWTPayload | null {
    try {
      // 处理token格式
      const processedToken = token.replace(/_/g, '/').replace(/-/g, '+');
      
      // 分割token获取payload部分
      const parts = processedToken.split('.');
      if (parts.length !== 3) {
        throw new Error('Invalid JWT format');
      }
      
      // 解码payload
      const payload = parts[1];
      const decodedPayload = atob(payload);
      const jsonPayload = decodeURIComponent(escape(decodedPayload));
      
      return JSON.parse(jsonPayload);
    } catch (error) {
      console.error('Failed to decrypt JWT:', error);
      return null;
    }
  }

  /**
   * 将JWT时间戳转换为Date对象
   * 处理JWT标准时间戳格式
   */
  static getJWTDate(timestamp: number): Date {
    return new Date(timestamp * 1000);
  }

  /**
   * 检查令牌是否过期
   */
  static async isTokenExpired(token?: string): Promise<boolean> {
    try {
      const currentToken = token || await this.getToken();
      if (!currentToken) {
        return true;
      }

      const payload = this.decryptJWT(currentToken);
      if (!payload || !payload.exp) {
        return true;
      }

      const expirationDate = this.getJWTDate(payload.exp);
      const now = new Date();
      
      return now >= expirationDate;
    } catch (error) {
      console.error('Failed to check token expiration:', error);
      return true;
    }
  }

  /**
   * 获取令牌剩余有效时间（秒）
   */
  static async getTokenRemainingTime(token?: string): Promise<number> {
    try {
      const currentToken = token || await this.getToken();
      if (!currentToken) {
        return 0;
      }

      const payload = this.decryptJWT(currentToken);
      if (!payload || !payload.exp) {
        return 0;
      }

      const expirationDate = this.getJWTDate(payload.exp);
      const now = new Date();
      const remainingTime = Math.floor((expirationDate.getTime() - now.getTime()) / 1000);
      
      return Math.max(0, remainingTime);
    } catch (error) {
      console.error('Failed to get token remaining time:', error);
      return 0;
    }
  }

  /**
   * 自动刷新令牌检查
   */
  static async shouldRefreshToken(token?: string): Promise<boolean> {
    const remainingTime = await this.getTokenRemainingTime(token);
    // 如果剩余时间少于5分钟，则需要刷新
    return remainingTime > 0 && remainingTime < 300;
  }
}
