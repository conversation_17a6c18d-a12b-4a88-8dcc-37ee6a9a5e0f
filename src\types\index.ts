/**
 * 类型定义导出文件
 */

export * from './auth';
export * from './common';

// React Native相关类型
export type { ViewStyle, TextStyle, ImageStyle } from 'react-native';

// 导航相关类型
export type RootStackParamList = {
  Login: undefined;
  Home: undefined;
  Dashboard: undefined;
};

// 屏幕组件Props类型
export interface ScreenProps {
  navigation?: any;
  route?: any;
}

// 组件基础Props类型
export interface BaseComponentProps {
  style?: ViewStyle;
  testID?: string;
}

// 按钮Props类型
export interface ButtonProps extends BaseComponentProps {
  title: string;
  onPress: () => void;
  disabled?: boolean;
  loading?: boolean;
  variant?: 'primary' | 'secondary' | 'outline' | 'text';
  size?: 'small' | 'medium' | 'large';
}

// 输入框Props类型
export interface InputProps extends BaseComponentProps {
  value: string;
  onChangeText: (text: string) => void;
  placeholder?: string;
  secureTextEntry?: boolean;
  keyboardType?: 'default' | 'email-address' | 'numeric' | 'phone-pad';
  autoCapitalize?: 'none' | 'sentences' | 'words' | 'characters';
  autoCorrect?: boolean;
  editable?: boolean;
  maxLength?: number;
  multiline?: boolean;
  numberOfLines?: number;
  error?: string;
  label?: string;
  required?: boolean;
}

// 卡片Props类型
export interface CardProps extends BaseComponentProps {
  children: React.ReactNode;
  padding?: number;
  margin?: number;
  elevation?: number;
  borderRadius?: number;
}

// 容器Props类型
export interface ContainerProps extends BaseComponentProps {
  children: React.ReactNode;
  padding?: number;
  margin?: number;
  backgroundColor?: string;
  flex?: number;
  justifyContent?: 'flex-start' | 'flex-end' | 'center' | 'space-between' | 'space-around' | 'space-evenly';
  alignItems?: 'flex-start' | 'flex-end' | 'center' | 'stretch' | 'baseline';
  flexDirection?: 'row' | 'column' | 'row-reverse' | 'column-reverse';
}
