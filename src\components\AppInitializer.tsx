import { useAppStore, useAuthStore } from '@/src/stores';
import React, { useEffect, useState } from 'react';
import { SystemThemeListener } from './SystemThemeListener';

export function AppInitializer({ children }: { children: React.ReactNode }) {
  const [isInitialized, setInitialized] = useState(false);
  const checkAuthStatus = useAuthStore((s) => s.checkAuthStatus);
  const initializeApp = useAppStore((s) => s.initializeApp);

  useEffect(() => {
    const initialize = async () => {
      try {
        // 初始化应用状态
        await initializeApp();
        // 检查认证状态
        await checkAuthStatus();
        setInitialized(true);
      } catch (error) {
        console.error('App initialization failed:', error);
        setInitialized(true); // 即使失败也要继续，避免卡住
      }
    };

    initialize();
  }, [initializeApp, checkAuthStatus]);

  // 在初始化完成前显示加载状态
  if (!isInitialized) {
    return null; // 或者返回一个加载组件
  }

  return (
    <SystemThemeListener>
      {children}
    </SystemThemeListener>
  );
}