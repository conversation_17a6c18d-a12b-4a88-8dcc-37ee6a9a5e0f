/**
 * 认证服务
 * HuiLink移动应用的用户认证API服务
 */

import { API_CONFIG } from '../constants';
import {
  ApiResponse,
  CaptchaInfo,
  LoginConfig,
  LoginInput,
  LoginOutput,
  UserInfo,
  WatermarkConfig
} from '../types';
import httpClient from './http';

export class AuthService {
  /**
   * 用户登录
   * 处理用户账号密码登录请求
   */
  static async login(loginData: LoginInput): Promise<LoginOutput> {
    try {
      const response = await httpClient.post<ApiResponse<LoginOutput>>(
        API_CONFIG.ENDPOINTS.LOGIN,
        loginData
      );
      
      if (response.data.result) {
        return response.data.result;
      }
      
      throw new Error('登录失败，请检查账号密码');
    } catch (error: any) {
      console.error('Login error:', error);
      throw new Error(error.message || '登录失败');
    }
  }

  /**
   * 获取验证码
   * 获取图形验证码用于登录验证
   */
  static async getCaptcha(): Promise<CaptchaInfo> {
    try {
      const response = await httpClient.get<ApiResponse<CaptchaInfo>>(
        API_CONFIG.ENDPOINTS.CAPTCHA
      );
      
      if (response.data.result) {
        return response.data.result;
      }
      
      throw new Error('获取验证码失败');
    } catch (error: any) {
      console.error('Get captcha error:', error);
      throw new Error(error.message || '获取验证码失败');
    }
  }

  /**
   * 获取用户信息
   * 获取当前登录用户的详细信息
   */
  static async getUserInfo(): Promise<UserInfo> {
    try {
      const response = await httpClient.get<ApiResponse<UserInfo>>(
        API_CONFIG.ENDPOINTS.USER_INFO
      );
      
      if (response.data.result) {
        return response.data.result;
      }
      
      throw new Error('获取用户信息失败');
    } catch (error: any) {
      console.error('Get user info error:', error);
      throw new Error(error.message || '获取用户信息失败');
    }
  }

  /**
   * 获取登录配置
   * 获取登录相关的配置信息
   */
  static async getLoginConfig(): Promise<LoginConfig> {
    try {
      const response = await httpClient.get<ApiResponse<LoginConfig>>(
        API_CONFIG.ENDPOINTS.LOGIN_CONFIG
      );
      
      if (response.data.result) {
        return response.data.result;
      }
      
      // 如果获取失败，返回默认配置
      return {
        captchaEnabled: true,
        secondVerEnabled: false,
      };
    } catch (error: any) {
      console.error('Get login config error:', error);
      // 获取配置失败时返回默认配置，不抛出错误
      return {
        captchaEnabled: true,
        secondVerEnabled: false,
      };
    }
  }

  /**
   * 获取水印配置
   * 获取应用水印显示配置
   */
  static async getWatermarkConfig(): Promise<WatermarkConfig> {
    try {
      const response = await httpClient.get<ApiResponse<WatermarkConfig>>(
        API_CONFIG.ENDPOINTS.WATERMARK_CONFIG
      );
      
      if (response.data.result) {
        return response.data.result;
      }
      
      // 如果获取失败，返回默认配置
      return {
        watermarkEnabled: false,
      };
    } catch (error: any) {
      console.error('Get watermark config error:', error);
      // 获取配置失败时返回默认配置，不抛出错误
      return {
        watermarkEnabled: false,
      };
    }
  }

  /**
   * 刷新令牌
   * 用于自动刷新过期的访问令牌
   */
  static async refreshToken(refreshToken: string): Promise<LoginOutput> {
    try {
      const response = await httpClient.post<ApiResponse<LoginOutput>>(
        '/api/sysAuth/refresh',
        { refreshToken }
      );
      
      if (response.data.result) {
        return response.data.result;
      }
      
      throw new Error('刷新令牌失败');
    } catch (error: any) {
      console.error('Refresh token error:', error);
      throw new Error(error.message || '刷新令牌失败');
    }
  }

  /**
   * 用户登出
   * 清除服务端会话（如果需要）
   */
  static async logout(): Promise<void> {
    try {
      // 这里可以调用服务端登出接口
      // await httpClient.post('/api/sysAuth/logout');
      console.log('User logged out');
    } catch (error: any) {
      console.error('Logout error:', error);
      // 登出失败不抛出错误，因为本地清理更重要
    }
  }
}
