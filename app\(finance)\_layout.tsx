/**
 * 财务模块布局
 * 管理财务相关页面的路由配置
 */

import { Stack } from 'expo-router';
import React from 'react';

export default function FinanceLayout() {
  return (
    <Stack>
      <Stack.Screen 
        name="prepay" 
        options={{ 
          headerShown: false,
          title: '预收款'
        }} 
      />
      <Stack.Screen 
        name="prepayreport" 
        options={{ 
          headerShown: false,
          title: '收款报表'
        }} 
      />
    </Stack>
  );
}
