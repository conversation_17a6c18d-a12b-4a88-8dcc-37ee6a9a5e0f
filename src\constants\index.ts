/**
 * 常量导出文件
 */

export * from './config';

// 应用状态常量
export const APP_STATES = {
  LOADING: 'loading',
  SUCCESS: 'success',
  ERROR: 'error',
  IDLE: 'idle',
} as const;

// 认证状态常量
export const AUTH_STATES = {
  AUTHENTICATED: 'authenticated',
  UNAUTHENTICATED: 'unauthenticated',
  CHECKING: 'checking',
} as const;

// 存储键名常量
export const STORAGE_KEYS = {
  TOKEN: 'access-token',
  USER_INFO: 'userInfo',
  THEME_CONFIG: 'themeConfig',
  LANGUAGE: 'language',
} as const;

// 路由名称常量
export const ROUTE_NAMES = {
  LOGIN: 'login',
  HOME: 'home',
  DASHBOARD: 'dashboard',
} as const;

// HTTP状态码常量
export const HTTP_STATUS = {
  OK: 200,
  UNAUTHORIZED: 401,
  FORBIDDEN: 403,
  NOT_FOUND: 404,
  INTERNAL_SERVER_ERROR: 500,
} as const;

// 请求方法常量
export const HTTP_METHODS = {
  GET: 'GET',
  POST: 'POST',
  PUT: 'PUT',
  DELETE: 'DELETE',
  PATCH: 'PATCH',
} as const;

// 内容类型常量
export const CONTENT_TYPES = {
  JSON: 'application/json',
  FORM_DATA: 'multipart/form-data',
  URL_ENCODED: 'application/x-www-form-urlencoded',
} as const;
