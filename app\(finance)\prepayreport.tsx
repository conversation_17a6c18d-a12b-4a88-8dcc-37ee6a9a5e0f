/**
 * 收款报表页面
 * 显示预收款的统计报表和明细信息
 */

import { Ionicons } from '@expo/vector-icons';
import DateTimePicker from '@react-native-community/datetimepicker';
import { router, Stack } from 'expo-router';
import React, { useEffect, useState } from 'react';
import { Alert, FlatList, Modal, Platform, StyleSheet, TouchableOpacity, View } from 'react-native';
import { Button, Card, Container, Text } from '../../src/components/ui';
import { useTheme } from '../../src/stores/appStore';

// 报表数据类型
interface ReportItem {
  id: string;
  newko: string; // 客户编号
  bpName: string; // 客户名称
  wrbtr: number; // 支付金额
  payTime?: string; // 支付时间
}

const PrepayReportScreen: React.FC = () => {
  const { theme } = useTheme();
  
  // 查询参数
  const [queryParams, setQueryParams] = useState({
    startDate: new Date().toISOString().split('T')[0], // 默认今天
  });

  // 报表数据
  const [tableData, setTableData] = useState<ReportItem[]>([]);
  const [loading, setLoading] = useState(false);
  
  // 明细弹窗
  const [detailVisible, setDetailVisible] = useState(false);
  const [selectedItem, setSelectedItem] = useState<ReportItem | null>(null);

  // 日期选择器状态
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [tempDate, setTempDate] = useState<Date>(new Date());

  // 页面加载时获取数据
  useEffect(() => {
    handleQuery();
  }, []);

  // 查询数据
  const handleQuery = async () => {
    if (!queryParams.startDate) {
      Alert.alert('提示', '请选择查询日期');
      return;
    }

    setLoading(true);
    
    try {
      // TODO: 实现实际的API调用
      // 模拟数据
      const mockData: ReportItem[] = [
        {
          id: '1',
          newko: 'C001',
          bpName: '张三',
          wrbtr: 5000.00,
          payTime: '2025-01-15 10:30:00',
        },
        {
          id: '2',
          newko: 'C002',
          bpName: '李四汽车销售有限公司',
          wrbtr: 15000.00,
          payTime: '2025-01-15 14:20:00',
        },
        {
          id: '3',
          newko: 'C003',
          bpName: '王五',
          wrbtr: 8000.00,
          payTime: '2025-01-15 16:45:00',
        },
                {
          id: '4',
          newko: 'C001',
          bpName: '张三1',
          wrbtr: 5000.00,
          payTime: '2025-02-15 10:30:00',
        },
        {
          id: '5',
          newko: 'C002',
          bpName: '李四汽车销售有限公司1',
          wrbtr: 15000.00,
          payTime: '2025-02-15 14:20:00',
        },
        {
          id: '6',
          newko: 'C003',
          bpName: '王五1',
          wrbtr: 8000.00,
          payTime: '2025-02-15 16:45:00',
        },
           {
          id: '7',
          newko: 'C001',
          bpName: '张三2',
          wrbtr: 5000.00,
          payTime: '2025-03-15 10:30:00',
        },
        {
          id: '8',
          newko: 'C002',
          bpName: '李四汽车销售有限公司2',
          wrbtr: 15000.00,
          payTime: '2025-03-15 14:20:00',
        },
        {
          id: '9',
          newko: 'C003',
          bpName: '王五2',
          wrbtr: 8000.00,
          payTime: '2025-03-15 16:45:00',
        },
      ];
      
      await new Promise(resolve => setTimeout(resolve, 1000)); // 模拟网络延迟
      setTableData(mockData);
    } catch (error) {
      Alert.alert('查询失败', '请稍后重试');
    } finally {
      setLoading(false);
    }
  };

  // 格式化金额
  const formatAmount = (amount: number): string => {
    return `¥${amount.toLocaleString('zh-CN', { minimumFractionDigits: 2 })}`;
  };

  // 显示明细
  const showDetail = (item: ReportItem) => {
    setSelectedItem(item);
    setDetailVisible(true);
  };

  // 处理日期选择 - iOS端只更新临时日期，不关闭选择器
  const handleDateChange = (_event: any, selectedDate?: Date) => {
    if (Platform.OS === 'ios') {
      // iOS端：只更新临时日期，不关闭选择器
      if (selectedDate) {
        setTempDate(selectedDate);
      }
    } else {
      // Android端：保持原有逻辑
      setShowDatePicker(false);
      if (selectedDate) {
        const formattedDate = selectedDate.toISOString().split('T')[0];
        setQueryParams(prev => ({ ...prev, startDate: formattedDate }));
      }
    }
  };

  // 确认日期选择 - iOS端专用
  const confirmDateSelection = () => {
    const formattedDate = tempDate.toISOString().split('T')[0];
    setQueryParams(prev => ({ ...prev, startDate: formattedDate }));
    setShowDatePicker(false);
  };

  // 取消日期选择 - iOS端专用
  const cancelDateSelection = () => {
    setShowDatePicker(false);
    // 重置临时日期为当前选中的日期
    setTempDate(new Date(queryParams.startDate || new Date().toISOString().split('T')[0]));
  };

  // 显示日期选择器
  const showDatePickerModal = () => {
    // 初始化临时日期为当前选中的日期
    setTempDate(new Date(queryParams.startDate || new Date().toISOString().split('T')[0]));
    setShowDatePicker(true);
  };

  // 渲染列表项
  const renderItem = ({ item }: { item: ReportItem }) => (
    <Card padding={4} elevation={1} style={styles.listItem}>
      <View style={styles.itemHeader}>
        <View style={styles.itemInfo}>
          <Text variant="body1" weight="semibold" numberOfLines={1}>
            {item.bpName}
          </Text>
          <Text variant="body2" color="secondary" style={styles.customerCode}>
            客户编号: {item.newko}
          </Text>
        </View>
        <View style={styles.itemAmount}>
          <Text variant="h6" weight="semibold" color="primary">
            {formatAmount(item.wrbtr)}
          </Text>
        </View>
      </View>
      
      <View style={styles.itemFooter}>
        {item.payTime && (
          <Text variant="body2" color="secondary">
            支付时间: {item.payTime}
          </Text>
        )}
        <TouchableOpacity
          style={styles.detailButton}
          onPress={() => showDetail(item)}
        >
          <Ionicons name="information-circle-outline" size={16} color={theme.colors.primary} />
          <Text variant="body2" color="primary" style={styles.detailText}>
            查看明细
          </Text>
        </TouchableOpacity>
      </View>
    </Card>
  );

  // 计算总金额
  const totalAmount = tableData.reduce((sum, item) => sum + item.wrbtr, 0);

  // 返回按钮
  const renderBackButton = () => (
    <TouchableOpacity
      onPress={() => router.back()}
      style={styles.backButton}
    >
      <Ionicons name="arrow-back" size={24} color={theme.colors.text.primary} />
    </TouchableOpacity>
  );

  return (
    <>
      <Stack.Screen
        options={{
          title: '收款报表',
          headerShown: true,
          headerLeft: renderBackButton,
          headerStyle: {
            backgroundColor: theme.colors.background.primary,
          },
          headerTitleStyle: {
            color: theme.colors.text.primary,
          },
        }}
      />
      
      <View style={[styles.container, { backgroundColor: theme.colors.background.secondary }]}>
        <Container flex={1} padding={4}>
          {/* 查询条件卡片 */}
          <Card padding={6} elevation={2} style={styles.queryCard}>
            <Text variant="h6" weight="semibold" style={styles.sectionTitle}>
              查询条件
            </Text>
            
            <View style={styles.queryRow}>
              <View style={styles.dateInput}>
                <Text variant="body2" style={styles.label}>查询日期</Text>
                <TouchableOpacity
                  style={styles.dateInputContainer}
                  onPress={showDatePickerModal}
                >
                  <Text style={styles.dateInputText}>
                    {queryParams.startDate || 'YYYY-MM-DD'}
                  </Text>
                  <Ionicons name="calendar-outline" size={20} color={theme.colors.text.secondary} />
                </TouchableOpacity>
              </View>
              
              <Button
                title="查询"
                onPress={handleQuery}
                loading={loading}
                style={styles.queryButton}
              />
            </View>
          </Card>

          {/* 统计信息卡片 */}
          {tableData.length > 0 && (
            <Card padding={6} elevation={2} style={styles.summaryCard}>
              <View style={styles.summaryRow}>
                <View style={styles.summaryItem}>
                  <Text variant="body2" color="secondary">总笔数</Text>
                  <Text variant="h5" weight="semibold">{tableData.length}</Text>
                </View>
                <View style={styles.summaryItem}>
                  <Text variant="body2" color="secondary">总金额</Text>
                  <Text variant="h5" weight="semibold" color="primary">
                    {formatAmount(totalAmount)}
                  </Text>
                </View>
              </View>
            </Card>
          )}

          {/* 报表列表 */}
          <Card padding={0} elevation={2} style={styles.listCard}>
            <View style={styles.listHeader}>
              <Text variant="h6" weight="semibold">收款明细</Text>
            </View>
            
            <FlatList
              data={tableData}
              renderItem={renderItem}
              keyExtractor={(item) => item.id}
              showsVerticalScrollIndicator={false}
              refreshing={loading}
              onRefresh={handleQuery}
              ListEmptyComponent={
                <View style={styles.emptyContainer}>
                  <Ionicons name="document-outline" size={48} color={theme.colors.text.secondary} />
                  <Text variant="body1" color="secondary" style={styles.emptyText}>
                    {loading ? '加载中...' : '暂无数据'}
                  </Text>
                </View>
              }
              contentContainerStyle={styles.listContent}
            />
          </Card>
        </Container>

        {/* 明细弹窗 */}
        <Modal
          visible={detailVisible}
          transparent
          animationType="fade"
          onRequestClose={() => setDetailVisible(false)}
        >
          <View style={styles.modalOverlay}>
            <View style={styles.modalContent}>
              <View style={styles.modalHeader}>
                <Text variant="h6" weight="semibold">收款明细</Text>
                <TouchableOpacity
                  onPress={() => setDetailVisible(false)}
                  style={styles.closeButton}
                >
                  <Ionicons name="close" size={24} color={theme.colors.text.primary} />
                </TouchableOpacity>
              </View>
              
              {selectedItem && (
                <View style={styles.modalBody}>
                  <View style={styles.detailRow}>
                    <Text variant="body2" color="secondary">客户编号</Text>
                    <Text variant="body1">{selectedItem.newko}</Text>
                  </View>
                  <View style={styles.detailRow}>
                    <Text variant="body2" color="secondary">客户名称</Text>
                    <Text variant="body1">{selectedItem.bpName}</Text>
                  </View>
                  <View style={styles.detailRow}>
                    <Text variant="body2" color="secondary">支付金额</Text>
                    <Text variant="h6" weight="semibold" color="primary">
                      {formatAmount(selectedItem.wrbtr)}
                    </Text>
                  </View>
                  {selectedItem.payTime && (
                    <View style={styles.detailRow}>
                      <Text variant="body2" color="secondary">支付时间</Text>
                      <Text variant="body1">{selectedItem.payTime}</Text>
                    </View>
                  )}
                </View>
              )}
            </View>
          </View>
        </Modal>

        {/* 日期选择器 - 跨平台适配 */}
        {showDatePicker && Platform.OS === 'ios' && (
          <Modal
            transparent={true}
            animationType="slide"
            visible={showDatePicker}
            onRequestClose={() => setShowDatePicker(false)}
          >
            <View style={styles.datePickerModal}>
              <View style={styles.datePickerContainer}>
                <View style={styles.datePickerHeader}>
                  <TouchableOpacity
                    onPress={cancelDateSelection}
                    style={styles.datePickerButton}
                  >
                    <Text style={styles.datePickerButtonText}>取消</Text>
                  </TouchableOpacity>
                  <Text style={styles.datePickerTitle}>选择日期</Text>
                  <TouchableOpacity
                    onPress={confirmDateSelection}
                    style={styles.datePickerButton}
                  >
                    <Text style={{...styles.datePickerButtonText, color: '#007AFF', fontWeight: '600'}}>确定</Text>
                  </TouchableOpacity>
                </View>
                <View style={styles.datePickerWrapper}>
                  <DateTimePicker
                    value={tempDate}
                    mode="date"
                    display="spinner"
                    onChange={handleDateChange}
                    locale="zh-CN"
                    style={styles.datePicker}
                  />
                </View>
              </View>
            </View>
          </Modal>
        )}

        {/* Android 日期选择器 - 使用原生对话框 */}
        {showDatePicker && Platform.OS === 'android' && (
          <DateTimePicker
            value={new Date(queryParams.startDate || new Date().toISOString().split('T')[0])}
            mode="date"
            display="default"
            onChange={handleDateChange}
          />
        )}
      </View>
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  backButton: {
    padding: 8,
  },
  queryCard: {
    marginBottom: 12,
  },
  summaryCard: {
    marginBottom: 12,
  },
  listCard: {
    flex: 1,
  },
  sectionTitle: {
    marginBottom: 16,
  },
  queryRow: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    gap: 12,
  },
  dateInput: {
    flex: 1,
  },
  dateInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderWidth: 2,
    borderColor: '#e0e0e0',
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: '#fff',
    minHeight: 56,
  },
  dateInputText: {
    fontSize: 16,
    color: '#333',
    flex: 1,
  },
  label: {
    marginBottom: 8,
    fontWeight: '500',
  },
  queryButton: {
    minWidth: 80,
  },
  summaryRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  summaryItem: {
    alignItems: 'center',
  },
  listHeader: {
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  listContent: {
    padding: 12,
  },
  listItem: {
    marginBottom: 8,
  },
  itemHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  itemInfo: {
    flex: 1,
    marginRight: 12,
  },
  customerCode: {
    marginTop: 4,
  },
  itemAmount: {
    alignItems: 'flex-end',
  },
  itemFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  detailButton: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
  },
  detailText: {
    fontSize: 12,
  },
  emptyContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 48,
  },
  emptyText: {
    marginTop: 12,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  modalContent: {
    backgroundColor: 'white',
    borderRadius: 12,
    margin: 20,
    minWidth: 300,
    maxWidth: '90%',
  },
  modalHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  closeButton: {
    padding: 4,
  },
  modalBody: {
    padding: 16,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#f8f8f8',
  },
  // 日期选择器样式
  datePickerModal: {
    flex: 1,
    justifyContent: 'flex-end',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  datePickerContainer: {
    backgroundColor: '#fff',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingBottom: 34, // 为iPhone底部安全区域留空间
  },
  datePickerHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  datePickerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#333',
  },
  datePickerButton: {
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  datePickerButtonText: {
    fontSize: 16,
    color: '#666',
  },
  datePickerWrapper: {
    width: '100%',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 20,
  },
  datePicker: {
    height: 200,
    backgroundColor: '#fff',
    width: '100%',
    alignSelf: 'center',
  },
});

export default PrepayReportScreen;
