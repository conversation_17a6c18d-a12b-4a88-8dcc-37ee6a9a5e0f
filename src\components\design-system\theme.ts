/**
 * 主题系统
 * 支持亮色/暗色模式的主题配置
 */

import { Colors, Typography, Spacing, BorderRadius, Shadows, Duration } from './tokens';

// 主题接口定义
export interface Theme {
  colors: {
    primary: string;
    secondary: string;
    success: string;
    warning: string;
    error: string;
    info: string;
    background: {
      primary: string;
      secondary: string;
      tertiary: string;
      overlay: string;
    };
    text: {
      primary: string;
      secondary: string;
      tertiary: string;
      inverse: string;
      disabled: string;
    };
    border: {
      primary: string;
      secondary: string;
      tertiary: string;
    };
    surface: {
      primary: string;
      secondary: string;
      tertiary: string;
    };
  };
  typography: typeof Typography;
  spacing: typeof Spacing;
  borderRadius: typeof BorderRadius;
  shadows: typeof Shadows;
  duration: typeof Duration;
  isDark: boolean;
}

// 亮色主题
export const lightTheme: Theme = {
  colors: {
    primary: Colors.primary[500],
    secondary: Colors.secondary[500],
    success: Colors.success[500],
    warning: Colors.warning[500],
    error: Colors.error[500],
    info: Colors.info[500],
    background: {
      primary: Colors.background.primary,
      secondary: Colors.background.secondary,
      tertiary: Colors.background.tertiary,
      overlay: Colors.background.overlay,
    },
    text: {
      primary: Colors.text.primary,
      secondary: Colors.text.secondary,
      tertiary: Colors.text.placeholder,
      inverse: Colors.text.inverse,
      disabled: Colors.text.disabled,
    },
    border: {
      primary: Colors.border.base,
      secondary: Colors.border.light,
      tertiary: Colors.border.lighter,
    },
    surface: {
      primary: '#FFFFFF',
      secondary: '#F8F9FA',
      tertiary: '#F1F3F4',
    },
  },
  typography: Typography,
  spacing: Spacing,
  borderRadius: BorderRadius,
  shadows: Shadows,
  duration: Duration,
  isDark: false,
};

// 暗色主题
export const darkTheme: Theme = {
  colors: {
    primary: Colors.primary[400],
    secondary: Colors.secondary[400],
    success: Colors.success[400],
    warning: Colors.warning[400],
    error: Colors.error[400],
    info: Colors.info[400],
    background: {
      primary: '#121212',
      secondary: '#1E1E1E',
      tertiary: '#2D2D2D',
      overlay: 'rgba(0, 0, 0, 0.7)',
    },
    text: {
      primary: '#FFFFFF',
      secondary: '#B3B3B3',
      tertiary: '#808080',
      inverse: '#000000',
      disabled: '#666666',
    },
    border: {
      primary: '#404040',
      secondary: '#333333',
      tertiary: '#2A2A2A',
    },
    surface: {
      primary: '#1E1E1E',
      secondary: '#2D2D2D',
      tertiary: '#3A3A3A',
    },
  },
  typography: Typography,
  spacing: Spacing,
  borderRadius: BorderRadius,
  shadows: {
    ...Shadows,
    // 暗色模式下的阴影调整
    sm: {
      ...Shadows.sm,
      shadowColor: '#000',
      shadowOpacity: 0.3,
    },
    base: {
      ...Shadows.base,
      shadowColor: '#000',
      shadowOpacity: 0.4,
    },
    md: {
      ...Shadows.md,
      shadowColor: '#000',
      shadowOpacity: 0.5,
    },
    lg: {
      ...Shadows.lg,
      shadowColor: '#000',
      shadowOpacity: 0.6,
    },
    xl: {
      ...Shadows.xl,
      shadowColor: '#000',
      shadowOpacity: 0.7,
    },
  },
  duration: Duration,
  isDark: true,
};

// 主题工具函数
export const getTheme = (isDark: boolean): Theme => {
  return isDark ? darkTheme : lightTheme;
};

// 主题颜色工具函数
export const getColorWithOpacity = (color: string, opacity: number): string => {
  // 如果是十六进制颜色
  if (color.startsWith('#')) {
    const hex = color.slice(1);
    const r = parseInt(hex.slice(0, 2), 16);
    const g = parseInt(hex.slice(2, 4), 16);
    const b = parseInt(hex.slice(4, 6), 16);
    return `rgba(${r}, ${g}, ${b}, ${opacity})`;
  }
  
  // 如果已经是rgba格式
  if (color.startsWith('rgba')) {
    return color.replace(/[\d\.]+\)$/g, `${opacity})`);
  }
  
  // 如果是rgb格式
  if (color.startsWith('rgb')) {
    return color.replace('rgb', 'rgba').replace(')', `, ${opacity})`);
  }
  
  return color;
};

// 获取对比色
export const getContrastColor = (backgroundColor: string, theme: Theme): string => {
  // 简单的对比色逻辑，可以根据需要扩展
  if (theme.isDark) {
    return theme.colors.text.primary;
  } else {
    return theme.colors.text.primary;
  }
};

// 主题常量
export const THEME_STORAGE_KEY = 'app_theme_mode';

// 默认主题
export const defaultTheme = lightTheme;
