/**
 * 图片资源类型定义
 * 提供类型安全的图片资源引用
 */

// 图标资源类型
export type IconAssets = {
  'app-icon': any;
  // 在这里添加更多图标类型
  // 'home': any;
  // 'settings': any;
  // 'profile': any;
};

// 按钮资源类型
export type ButtonAssets = {
  // 'primary': any;
  // 'secondary': any;
  // 'danger': any;
};

// Logo 资源类型
export type LogoAssets = {
  // 'company-logo': any;
  // 'app-logo': any;
};

// 所有图片资源类型
export type ImageAssets = {
  icons: IconAssets;
  buttons: ButtonAssets;
  logos: LogoAssets;
};

// 图片资源路径辅助函数
export const ImagePaths = {
  icons: {
    appIcon: require('@/assets/images/icons/app-icon.png'),
    // 在这里添加更多图标路径
    // home: require('@/assets/images/icons/home.png'),
    // settings: require('@/assets/images/icons/settings.png'),
  },
  buttons: {
    // primary: require('@/assets/images/buttons/primary.png'),
    // secondary: require('@/assets/images/buttons/secondary.png'),
  },
  logos: {
    // companyLogo: require('@/assets/images/logos/company-logo.png'),
    // appLogo: require('@/assets/images/logos/app-logo.png'),
  },
} as const;

// 使用示例：
// import { ImagePaths } from '@/src/types/images';
// <Image source={ImagePaths.icons.appIcon} />
