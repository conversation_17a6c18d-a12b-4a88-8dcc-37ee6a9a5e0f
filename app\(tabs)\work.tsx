/**
 * 工作 Tab 页面
 * 工作相关功能和任务管理
 */

import { router } from 'expo-router';
import React from 'react';
import { Alert, StyleSheet, TouchableOpacity, View } from 'react-native';
import { Button, Card, Container, Screen, Text } from '../../src/components/ui';
import SvgIcon from '../../src/components/ui/SvgIcon';
import { useAuthStore } from '../../src/stores';
import { useTheme } from '../../src/stores/appStore';

const WorkScreen: React.FC = () => {
  const { theme } = useTheme();
  const { user } = useAuthStore();

  // 工作模块数据
  const workModules = [
    {
      id: 1,
      title: '预收款',
      iconName: 'prepay' as const,
      iconColor: '#28A745',
      route: '/(finance)/prepay',
      description: '管理预收款项',
    },
    {
      id: 2,
      title: '收款报表',
      iconName: 'prepayreport' as const,
      iconColor: '#007BFF',
      route: '/(finance)/prepayreport',
      description: '查看收款统计',
    },
    {
      id: 3,
      title: '数据平台',
      iconName: 'dataplatform' as const,
      iconColor: '#6F42C1',
      route: '/(dataplatform)',
      description: '数据分析与报表',
    },
  ];

  // 处理工作模块点击
  const handleModulePress = (module: typeof workModules[0]) => {
    try {
      router.push(module.route as any);
    } catch (error) {
      console.error('路由跳转失败:', error);
      Alert.alert('跳转失败', '页面跳转出现错误，请稍后重试');
    }
  };

  return (
    <Screen backgroundColor={theme.colors.background.secondary}>
      <Container flex={1} padding={6}>
        {/* 工作台标题 */}
        <Card padding={6} elevation={2} style={{ marginBottom: theme.spacing[4] }}>
          <Container>
            <Text variant="h3" weight="semibold" style={{ marginBottom: theme.spacing[2] }}>
              工作台
            </Text>
            <Text variant="body2" color="secondary">
              {user?.realName || user?.account || '用户'}，欢迎使用工作台
            </Text>
          </Container>
        </Card>

        {/* 工作模块区域 */}
        <Card padding={6} elevation={2} style={{ marginBottom: theme.spacing[4] }}>
          <Container>
            <Text variant="h6" weight="semibold" style={{ marginBottom: theme.spacing[4] }}>
              工作功能
            </Text>

            <View style={styles.moduleGrid}>
              {workModules.map((module) => (
                <TouchableOpacity
                  key={module.id}
                  style={[styles.moduleItem, { backgroundColor: theme.colors.background.primary }]}
                  onPress={() => handleModulePress(module)}
                  activeOpacity={0.7}
                >
                  <View style={styles.moduleIconContainer}>
                    <SvgIcon
                      name={module.iconName}
                      size={32}
                      color={module.iconColor}
                    />
                  </View>
                  <Text variant="body2" weight="semibold" style={styles.moduleTitle}>
                    {module.title}
                  </Text>
                  <Text variant="caption" color="secondary" style={styles.moduleDescription}>
                    {module.description}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </Container>
        </Card>

        {/* 快捷操作 */}
        <Card padding={6} elevation={2}>
          <Container>
            <Text variant="h6" weight="semibold" style={{ marginBottom: theme.spacing[4] }}>
              快捷操作
            </Text>
            
            <View style={styles.quickActions}>
              <Button
                title="刷新数据"
                variant="outline"
                onPress={() => Alert.alert('提示', '数据已刷新')}
                style={styles.actionButton}
              />
              <Button
                title="同步状态"
                variant="outline"
                onPress={() => Alert.alert('提示', '状态已同步')}
                style={styles.actionButton}
              />
            </View>
          </Container>
        </Card>
      </Container>
    </Screen>
  );
};

const styles = StyleSheet.create({
  moduleGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
    gap: 16,
  },
  moduleItem: {
    width: '48%',
    minHeight: 120,
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    justifyContent: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  moduleIconContainer: {
    width: 48,
    height: 48,
    marginBottom: 8,
    alignItems: 'center',
    justifyContent: 'center',
  },
  moduleTitle: {
    textAlign: 'center',
    fontSize: 14,
    marginBottom: 4,
  },
  moduleDescription: {
    textAlign: 'center',
    fontSize: 11,
    lineHeight: 14,
  },
  quickActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 12,
  },
  actionButton: {
    flex: 1,
  },
});

export default WorkScreen;
