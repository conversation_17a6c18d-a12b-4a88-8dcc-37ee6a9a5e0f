/**
 * 预收款页面
 * 客户预收款信息录入和管理
 */

import { Ionicons } from '@expo/vector-icons';
import { router, Stack } from 'expo-router';
import React, { useState } from 'react';
import { Alert, Platform, ScrollView, StyleSheet, TextInput, TouchableOpacity, View } from 'react-native';
import { Card, Text } from '../../src/components/ui';
import { useTheme } from '../../src/stores/appStore';

// 改进的单选按钮组组件
interface RadioGroupProps {
  options: { label: string; value: string }[];
  selectedValue: string;
  onValueChange: (value: string) => void;
  compact?: boolean; // 新增紧凑模式
  theme?: any; // 添加主题参数
}

const RadioGroup: React.FC<RadioGroupProps> = ({ options, selectedValue, onValueChange, compact = false, theme }) => {
  return (
    <View style={compact ? getCompactRadioGroupStyle() : getRadioGroupStyle()}>
      {options.map((option) => (
        <TouchableOpacity
          key={option.value}
          style={[
            compact ? getCompactRadioOptionStyle(theme) : getRadioOptionStyle(theme),
            selectedValue === option.value && (compact ? getCompactRadioOptionSelectedStyle(theme) : getRadioOptionSelectedStyle(theme))
          ]}
          onPress={() => onValueChange(option.value)}
          activeOpacity={0.7}
        >
          <Text
            variant="body2"
            style={{
              ...(compact ? getCompactRadioTextStyle(theme) : getRadioTextStyle(theme)),
              ...(selectedValue === option.value ? getRadioTextSelectedStyle() : {})
            }}
          >
            {option.label}
          </Text>
        </TouchableOpacity>
      ))}
    </View>
  );
};

// 输入过滤函数
const filterPhoneInput = (text: string): string => {
  // 只允许数字、空格、+、-、()等电话号码字符
  return text.replace(/[^0-9\s+\-()]/g, '');
};

const filterNumericInput = (text: string): string => {
  // 只允许数字和一个小数点
  const filtered = text.replace(/[^0-9.]/g, '');
  // 确保只有一个小数点
  const parts = filtered.split('.');
  if (parts.length > 2) {
    return parts[0] + '.' + parts.slice(1).join('');
  }
  return filtered;
};

const PrepayScreen: React.FC = () => {
  const { theme } = useTheme();

  // 表单数据
  const [formData, setFormData] = useState({
    customerName: '',
    title: '0001', // 0001: 女士, 0002: 先生, 0003: 公司
    phone: '',
    store: '',
    profitCenter: '05', // 默认选择"二手车(服务)"
    amount: '',
  });

  // 表单错误
  const [formErrors, setFormErrors] = useState<Record<string, string>>({});

  // 加载状态
  const [isLoading, setIsLoading] = useState(false);

  // 称谓选项
  const titleOptions = [
    { label: '女士', value: '0001' },
    { label: '先生', value: '0002' },
    { label: '公司', value: '0003' },
  ];

  // 利润中心选项
  const profitCenterOptions = [
    { label: '整车销售', value: '01' },
    { label: '售后服务', value: '02' },
    { label: '装饰', value: '03' },
    { label: '二手车(购销)', value: '04' },
    { label: '二手车(服务)', value: '05' },
  ];

  // 处理输入变化
  const handleInputChange = (field: keyof typeof formData, value: string) => {
    let filteredValue = value;

    // 根据字段类型应用不同的过滤器
    switch (field) {
      case 'phone':
        filteredValue = filterPhoneInput(value);
        break;
      case 'amount':
        filteredValue = filterNumericInput(value);
        break;
      default:
        // 其他字段不过滤
        break;
    }

    setFormData(prev => ({ ...prev, [field]: filteredValue }));

    // 清除对应字段的错误
    if (formErrors[field]) {
      setFormErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  // 表单验证
  const validateForm = (): boolean => {
    const errors: Record<string, string> = {};

    if (!formData.customerName.trim()) {
      errors.customerName = '客户名称不能为空';
    }

    if (!formData.phone.trim()) {
      errors.phone = '联系电话不能为空';
    } else if (!/^1[3-9]\d{9}$/.test(formData.phone.trim())) {
      errors.phone = '请输入正确的手机号码';
    }

    if (!formData.store.trim()) {
      errors.store = '请选择门店';
    }

    if (!formData.amount.trim()) {
      errors.amount = '预收定金不能为空';
    } else if (isNaN(Number(formData.amount)) || Number(formData.amount) <= 0) {
      errors.amount = '请输入正确的金额';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // 提交表单
  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }

    setIsLoading(true);

    try {
      // TODO: 实现实际的API调用
      await new Promise(resolve => setTimeout(resolve, 1000)); // 模拟API调用

      Alert.alert(
        '提交成功',
        '预收款信息已成功提交',
        [
          {
            text: '确定',
            onPress: () => router.back(),
          }
        ]
      );
    } catch (error) {
      Alert.alert('提交失败', '请稍后重试');
    } finally {
      setIsLoading(false);
    }
  };

  // 清除表单
  const handleClear = () => {
    setFormData({
      customerName: '',
      title: '0001',
      phone: '',
      store: '',
      profitCenter: '05', // 默认选择"二手车(服务)"
      amount: '',
    });
    setFormErrors({});
  };

  // 返回按钮
  const renderBackButton = () => (
    <TouchableOpacity
      onPress={() => router.back()}
      style={styles.backButton}
    >
      <Ionicons name="arrow-back" size={24} color={theme.colors.text.primary} />
    </TouchableOpacity>
  );

  return (
    <>
      <Stack.Screen
        options={{
          title: '预收款',
          headerShown: true,
          headerLeft: renderBackButton,
          headerStyle: {
            backgroundColor: theme.colors.background.primary,
          },
          headerTitleStyle: {
            color: theme.colors.text.primary,
          },
        }}
      />
      
      <View style={[styles.mainContainer, { backgroundColor: theme.colors.background.secondary }]}>
        <ScrollView
          style={styles.container}
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
        >
          <View style={styles.contentPadding}>
            {/* 单一卡片容器 */}
            <Card
              padding={12}
              elevation={Platform.OS === 'android' ? 2 : 1}
              style={styles.singleCard}
            >
              <Text variant="h6" weight="semibold" style={[styles.sectionTitle, { color: theme.colors.text.primary }]}>
                客户信息
              </Text>

              {/* 客户名称 */}
              <View style={styles.formItem}>
                <Text variant="body2" style={[styles.label, { color: theme.colors.text.primary }]}>客户名称 <Text style={{ color: '#EF4444' }}>*</Text></Text>
                <TextInput
                  value={formData.customerName}
                  onChangeText={(value) => handleInputChange('customerName', value)}
                  placeholder="请输入客户名称"
                  placeholderTextColor={theme.colors.text.tertiary}
                  style={getInputStyle(theme)}
                />
                {formErrors.customerName && (
                  <Text style={styles.errorText}>{formErrors.customerName}</Text>
                )}
              </View>

              {/* 称谓 */}
              <View style={styles.formItemCompact}>
                <Text variant="body2" style={[styles.label, { color: theme.colors.text.primary }]}>称谓 <Text style={{ color: '#EF4444' }}>*</Text></Text>
                <RadioGroup
                  options={titleOptions}
                  selectedValue={formData.title}
                  onValueChange={(value) => handleInputChange('title', value)}
                  theme={theme}
                />
              </View>

              {/* 联系电话 */}
              <View style={styles.formItem}>
                <Text variant="body2" style={[styles.label, { color: theme.colors.text.primary }]}>联系电话 <Text style={{ color: '#EF4444' }}>*</Text></Text>
                <TextInput
                  value={formData.phone}
                  onChangeText={(value) => handleInputChange('phone', value)}
                  placeholder="请输入联系电话"
                  placeholderTextColor={theme.colors.text.tertiary}
                  keyboardType="phone-pad"
                  style={getInputStyle(theme)}
                />
                {formErrors.phone && (
                  <Text style={styles.errorText}>{formErrors.phone}</Text>
                )}
              </View>

              {/* 门店名称 */}
              <View style={styles.formItem}>
                <Text variant="body2" style={[styles.label, { color: theme.colors.text.primary }]}>门店名称 <Text style={{ color: '#EF4444' }}>*</Text></Text>
                <TextInput
                  value={formData.store}
                  onChangeText={(value) => handleInputChange('store', value)}
                  placeholder="请选择门店"
                  placeholderTextColor={theme.colors.text.tertiary}
                  style={getInputStyle(theme)}
                />
                {formErrors.store && (
                  <Text style={styles.errorText}>{formErrors.store}</Text>
                )}
              </View>

              {/* 利润中心 */}
              <View style={styles.formItemCompact}>
                <Text variant="body2" style={[styles.label, { color: theme.colors.text.primary }]}>利润中心</Text>
                <RadioGroup
                  options={profitCenterOptions}
                  selectedValue={formData.profitCenter}
                  onValueChange={(value) => handleInputChange('profitCenter', value)}
                  compact={true}
                  theme={theme}
                />
              </View>

              {/* 预收定金 */}
              <View style={styles.formItem}>
                <Text variant="body2" style={[styles.label, { color: theme.colors.text.primary }]}>预收定金 <Text style={{ color: '#EF4444' }}>*</Text></Text>
                <TextInput
                  value={formData.amount}
                  onChangeText={(value) => handleInputChange('amount', value)}
                  placeholder="请输入预收定金金额"
                  placeholderTextColor={theme.colors.text.tertiary}
                  keyboardType="numeric"
                  style={getInputStyle(theme)}
                />
                {formErrors.amount && (
                  <Text style={styles.errorText}>{formErrors.amount}</Text>
                )}
              </View>
            </Card>

            {/* 操作按钮 */}
            <View style={styles.buttonContainer}>
              <View style={styles.buttonRow}>
                <TouchableOpacity
                  onPress={handleSubmit}
                  disabled={isLoading}
                  style={getSubmitButtonStyle(theme)}
                  activeOpacity={0.8}
                >
                  <Text style={getSubmitButtonTextStyle()}>
                    {isLoading ? '提交中...' : '提交'}
                  </Text>
                </TouchableOpacity>
                <TouchableOpacity
                  onPress={handleClear}
                  disabled={isLoading}
                  style={getClearButtonStyle(theme)}
                  activeOpacity={0.8}
                >
                  <Text style={getClearButtonTextStyle(theme)}>清除</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        </ScrollView>
      </View>
    </>
  );
};

// 样式函数 - 简单的单层输入框样式
const getInputStyle = (theme: any) => ({
  borderRadius: 8,
  minHeight: 48,
  paddingHorizontal: 16,
  paddingVertical: 12,
  borderWidth: 2,
  borderColor: theme.colors.border.primary,
  backgroundColor: theme.colors.surface.primary,
  fontSize: 16,
  color: theme.colors.text.primary,
  ...(Platform.OS === 'android' && {
    elevation: 0,
    shadowOpacity: 0,
  }),
});

const getSubmitButtonStyle = (theme: any) => ({
  flex: 1,
  minHeight: 48,
  borderRadius: 8,
  backgroundColor: theme.colors.primary,
  alignItems: 'center' as const,
  justifyContent: 'center' as const,
});

const getClearButtonStyle = (theme: any) => ({
  flex: 1,
  minHeight: 48,
  borderRadius: 8,
  backgroundColor: theme.colors.surface.secondary,
  borderWidth: 1,
  borderColor: theme.colors.border.primary,
  alignItems: 'center' as const,
  justifyContent: 'center' as const,
});

const getSubmitButtonTextStyle = () => ({
  fontSize: 16,
  fontWeight: '600' as const,
  color: '#FFFFFF',
});

const getClearButtonTextStyle = (theme: any) => ({
  fontSize: 16,
  fontWeight: '600' as const,
  color: theme.colors.text.primary,
});

const getRadioGroupStyle = () => ({
  flexDirection: 'row' as const,
  flexWrap: 'wrap' as const,
  gap: 8,
  marginTop: 4,
});

const getRadioOptionStyle = (theme: any) => ({
  paddingHorizontal: 16,
  paddingVertical: 8,
  borderRadius: 20,
  borderWidth: 1,
  borderColor: theme?.colors?.border?.primary || '#D1D5DB',
  backgroundColor: theme?.colors?.surface?.primary || '#FFFFFF',
  marginRight: 8,
  marginBottom: 8,
  minWidth: 70,
  alignItems: 'center' as const,
  justifyContent: 'center' as const,
});

const getRadioOptionSelectedStyle = (theme: any) => ({
  borderColor: theme?.colors?.primary || '#3B82F6',
  backgroundColor: theme?.colors?.primary || '#3B82F6',
});

const getRadioTextStyle = (theme: any) => ({
  fontSize: 14,
  fontWeight: '500' as const,
  color: theme?.colors?.text?.secondary || '#6B7280',
});

const getRadioTextSelectedStyle = () => ({
  color: '#FFFFFF',
  fontWeight: '600' as const,
});

// 紧凑模式样式函数
const getCompactRadioGroupStyle = () => ({
  flexDirection: 'row' as const,
  flexWrap: 'wrap' as const,
  gap: 4,
  marginTop: 4,
});

const getCompactRadioOptionStyle = (theme: any) => ({
  paddingHorizontal: 8,
  paddingVertical: 8,
  borderRadius: 20,
  borderWidth: 1,
  borderColor: theme?.colors?.border?.primary || '#D1D5DB',
  backgroundColor: theme?.colors?.surface?.primary || '#FFFFFF',
  marginRight: 4,
  marginBottom: 4,
  minWidth: 60,
  alignItems: 'center' as const,
  justifyContent: 'center' as const,
});

const getCompactRadioOptionSelectedStyle = (theme: any) => ({
  borderColor: theme?.colors?.primary || '#3B82F6',
  backgroundColor: theme?.colors?.primary || '#3B82F6',
});

const getCompactRadioTextStyle = (theme: any) => ({
  fontSize: 12,
  fontWeight: '500' as const,
  color: theme?.colors?.text?.secondary || '#6B7280',
});

const styles = StyleSheet.create({
  mainContainer: {
    flex: 1,
    minHeight: '100%',
  },
  container: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    paddingBottom: Platform.OS === 'android' ? 24 : 16,
  },
  contentPadding: {
    padding: 12,
  },
  backButton: {
    padding: 12,
    borderRadius: 8,
    // 增加触摸区域
    minWidth: 44,
    minHeight: 44,
    alignItems: 'center',
    justifyContent: 'center',
  },
  singleCard: {
    marginBottom: 16,
    borderRadius: 12,
    // 安卓优化的阴影
    ...(Platform.OS === 'android' && {
      elevation: 2,
    }),
    // iOS优化的阴影
    ...(Platform.OS === 'ios' && {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.05,
      shadowRadius: 4,
    }),
  },
  sectionTitle: {
    marginBottom: 12,
    fontSize: 16,
    fontWeight: '600',
    // color will be applied inline with theme
  },
  formItem: {
    marginBottom: 16, // 增加底部间距
    minHeight: 90, // 增加预留错误提示空间高度
    position: 'relative', // 为绝对定位的错误提示添加相对定位容器
  },
  label: {
    marginBottom: 3,
    fontWeight: '500',
    fontSize: 14,
    lineHeight: 20,
    // color will be applied inline with theme
  },
  buttonContainer: {
    marginTop: 16,
    marginBottom: Platform.OS === 'android' ? 24 : 20,
    gap: 12,
  },
  buttonRow: {
    flexDirection: 'row',
    gap: 12,
  },
  errorText: {
    fontSize: 12,
    color: '#EF4444',
    marginTop: 4,
    marginLeft: 4,
    minHeight: 20, // 增加固定错误提示高度
    position: 'absolute', // 使用绝对定位避免影响布局
    bottom: -8, // 调整定位位置，使错误提示更贴近输入框
  },
  formItemCompact: {
    marginBottom: 12, // 减少底部间距
    minHeight: 60, // 减少预留空间高度，因为不需要错误提示空间
    position: 'relative',
  },
});

export default PrepayScreen;
