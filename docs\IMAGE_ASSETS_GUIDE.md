# 图片资源使用指南

## 📁 目录结构

```
assets/images/
├── icons/                 # 图标资源
│   ├── app-icon.png       # 1x (基础分辨率)
│   ├── <EMAIL>    # 2x (高分辨率)
│   ├── <EMAIL>    # 3x (超高分辨率)
│   ├── home.png           # 其他图标...
│   ├── <EMAIL>
│   └── <EMAIL>
├── buttons/               # 按钮相关图片
│   ├── primary.png
│   ├── <EMAIL>
│   └── <EMAIL>
└── logos/                 # Logo 和品牌图片
    ├── company-logo.png
    ├── <EMAIL>
    └── <EMAIL>
```

## 🎯 分辨率对应关系

| 设备类型 | 像素密度 | 文件命名 | 建议尺寸比例 |
|---------|---------|----------|-------------|
| 标准设备 | 1x | `image.png` | 基础尺寸 |
| 高分辨率设备 | 2x | `<EMAIL>` | 基础尺寸 × 2 |
| 超高分辨率设备 | 3x | `<EMAIL>` | 基础尺寸 × 3 |

## 💻 代码使用示例

### 基础用法

```tsx
import { Image } from 'expo-image';

// React Native 自动选择合适的分辨率
<Image 
  source={require('@/assets/images/icons/home.png')} 
  style={{ width: 24, height: 24 }} 
/>
```

### 在组件中使用

```tsx
import React from 'react';
import { View, StyleSheet } from 'react-native';
import { Image } from 'expo-image';

export default function MyComponent() {
  return (
    <View style={styles.container}>
      <Image 
        source={require('@/assets/images/icons/app-icon.png')} 
        style={styles.icon} 
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  icon: {
    width: 48,
    height: 48,
  },
});
```

## 📏 尺寸建议

### 图标尺寸
- **小图标**: 16x16, 24x24, 32x32
- **中等图标**: 48x48, 64x64
- **大图标**: 72x72, 96x96

### 按钮图片
- **小按钮**: 高度 32-40px
- **中等按钮**: 高度 44-48px
- **大按钮**: 高度 56-64px

## ✅ 最佳实践

1. **命名规范**
   - 使用描述性名称：`icon-home.png` 而不是 `ic_01.png`
   - 使用连字符分隔：`button-primary.png`
   - 保持一致的命名风格

2. **文件优化**
   - 使用 PNG 格式保证透明度
   - 压缩图片以减少应用体积
   - 移除不必要的元数据

3. **分辨率准备**
   - 始终提供 @2x 和 @3x 版本
   - 确保高分辨率版本清晰锐利
   - 测试在不同设备上的显示效果

4. **性能考虑**
   - 避免过大的图片文件
   - 考虑使用 WebP 格式（如果支持）
   - 合理使用图片缓存

## 🔧 添加新图片的步骤

1. **准备图片文件**
   ```
   my-icon.png      (24x24)
   <EMAIL>   (48x48)
   <EMAIL>   (72x72)
   ```

2. **放置到正确目录**
   ```
   assets/images/icons/
   ```

3. **在代码中使用**
   ```tsx
   <Image source={require('@/assets/images/icons/my-icon.png')} />
   ```

## 🚀 从原生资源迁移

如果您有原生 Android/iOS 资源：

### Android 密度映射
- `mdpi` → `image.png` (1x)
- `hdpi` → `image.png` (1.5x，使用1x)
- `xhdpi` → `<EMAIL>` (2x)
- `xxhdpi` → `<EMAIL>` (2x)
- `xxxhdpi` → `<EMAIL>` (3x)

### iOS 分辨率映射
- `@1x` → `image.png`
- `@2x` → `<EMAIL>`
- `@3x` → `<EMAIL>`

## 📱 测试建议

1. 在不同分辨率的设备上测试
2. 检查图片在暗色模式下的显示效果
3. 验证图片加载性能
4. 确保图片在不同屏幕尺寸下的适配效果
