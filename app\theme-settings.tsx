/**
 * Theme Settings Screen
 * Allows the user to select the app theme.
 */
import React from 'react';
import { View, TouchableOpacity, StyleSheet } from 'react-native';
import { Stack, useRouter } from 'expo-router';
import FontAwesome from '@expo/vector-icons/FontAwesome';
import { Screen, Text, Card, Container } from '../src/components/ui';
import { useTheme } from '../src/stores/appStore';

const ThemeSettingsScreen = () => {
  const { theme, themeMode, setTheme } = useTheme();
  const router = useRouter();

  const themeOptions = [
    { value: 'light', label: '浅色' },
    { value: 'dark', label: '深色' },
    { value: 'system', label: '跟随系统' },
  ];

  const handleThemeSelect = async (selectedTheme: 'light' | 'dark' | 'system') => {
    await setTheme(selectedTheme);
    // Vibrate or give some feedback if you want
    // Optionally navigate back
    if (router.canGoBack()) {
      router.back();
    }
  };

  return (
    <Screen backgroundColor={theme.colors.background.secondary}>
      <Stack.Screen 
        options={{ 
          title: '主题设置',
          headerStyle: {
            backgroundColor: theme.colors.background.primary,
          },
          headerTintColor: theme.colors.text.primary,
          headerTitleStyle: {
            color: theme.colors.text.primary,
          }
        }} 
      />
      <Container padding={6} flex={1}>
        <Card padding={6} elevation={2}>
          {themeOptions.map((option, index) => (
            <TouchableOpacity
              key={option.value}
              style={[
                styles.optionItem,
                { borderBottomColor: theme.colors.border.tertiary },
                // Remove border for the last item
                index === themeOptions.length - 1 && { borderBottomWidth: 0 }
              ]}
              onPress={() => handleThemeSelect(option.value as any)}
              activeOpacity={0.6}
            >
              <Text variant="body1">{option.label}</Text>
              {themeMode === option.value && (
                <FontAwesome
                  name="check"
                  size={20}
                  color={theme.colors.primary}
                />
              )}
            </TouchableOpacity>
          ))}
        </Card>
      </Container>
    </Screen>
  );
};

const styles = StyleSheet.create({
  optionItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 18,
    paddingHorizontal: 4,
    borderBottomWidth: 1,
  },
});

export default ThemeSettingsScreen;