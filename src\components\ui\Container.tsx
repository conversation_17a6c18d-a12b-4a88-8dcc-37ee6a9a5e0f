/**
 * Container 组件
 * 通用的容器组件，提供一致的布局和间距
 */

import React from 'react';
import { View, ViewStyle } from 'react-native';
import { useTheme } from '../../stores/appStore';
import { ContainerProps } from '../../types';

const Container: React.FC<ContainerProps> = ({
  children,
  padding,
  margin,
  backgroundColor,
  flex,
  justifyContent = 'flex-start',
  alignItems = 'stretch',
  flexDirection = 'column',
  style,
  testID,
}) => {
  const { theme } = useTheme();

  // 获取容器样式
  const getContainerStyle = (): ViewStyle => {
    const baseStyle: ViewStyle = {
      flexDirection,
      justifyContent,
      alignItems,
    };

    // 应用可选属性
    if (flex !== undefined) {
      baseStyle.flex = flex;
    }

    if (padding !== undefined) {
      baseStyle.padding = typeof padding === 'number' ? padding : theme.spacing[padding as keyof typeof theme.spacing];
    }

    if (margin !== undefined) {
      baseStyle.margin = typeof margin === 'number' ? margin : theme.spacing[margin as keyof typeof theme.spacing];
    }

    if (backgroundColor) {
      baseStyle.backgroundColor = backgroundColor;
    }

    return baseStyle;
  };

  return (
    <View style={[getContainerStyle(), style]} testID={testID}>
      {children}
    </View>
  );
};

export default Container;
