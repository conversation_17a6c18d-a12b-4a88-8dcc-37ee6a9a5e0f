import React from 'react';
import { View, StyleSheet, Text } from 'react-native';
import { Image } from 'expo-image';

/**
 * 示例组件：展示如何使用 React Native 标准多分辨率图片
 * 
 * React Native 会根据设备的像素密度自动选择合适的图片：
 * - 在 1x 设备上使用 app-icon.png
 * - 在 2x 设备上使用 <EMAIL>  
 * - 在 3x 设备上使用 <EMAIL>
 */
export default function ImageExample() {
  return (
    <View style={styles.container}>
      <Text style={styles.title}>多分辨率图片示例</Text>
      
      {/* 应用图标 - React Native 自动选择分辨率 */}
      <View style={styles.imageContainer}>
        <Image 
          source={require('@/assets/images/icons/app-icon.png')} 
          style={styles.appIcon} 
        />
        <Text style={styles.label}>应用图标</Text>
      </View>

      {/* 小图标示例 */}
      <View style={styles.imageContainer}>
        <Image 
          source={require('@/assets/images/icons/app-icon.png')} 
          style={styles.smallIcon} 
        />
        <Text style={styles.label}>小图标 (24x24)</Text>
      </View>

      {/* 中等图标示例 */}
      <View style={styles.imageContainer}>
        <Image 
          source={require('@/assets/images/icons/app-icon.png')} 
          style={styles.mediumIcon} 
        />
        <Text style={styles.label}>中等图标 (48x48)</Text>
      </View>

      <Text style={styles.note}>
        💡 提示：React Native 会根据设备像素密度自动选择最合适的图片分辨率
      </Text>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#f5f5f5',
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 30,
    color: '#333',
  },
  imageContainer: {
    alignItems: 'center',
    marginBottom: 20,
    padding: 15,
    backgroundColor: 'white',
    borderRadius: 10,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 5,
  },
  appIcon: {
    width: 72,
    height: 72,
    marginBottom: 8,
  },
  smallIcon: {
    width: 24,
    height: 24,
    marginBottom: 8,
  },
  mediumIcon: {
    width: 48,
    height: 48,
    marginBottom: 8,
  },
  label: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
  },
  note: {
    fontSize: 12,
    color: '#888',
    textAlign: 'center',
    marginTop: 20,
    paddingHorizontal: 20,
    fontStyle: 'italic',
  },
});
