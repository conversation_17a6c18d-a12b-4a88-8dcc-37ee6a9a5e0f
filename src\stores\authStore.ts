/**
 * 认证状态管理
 * 使用Zustand实现轻量级状态管理
 */

import AsyncStorage from "@react-native-async-storage/async-storage";
import { create } from "zustand";
import { AUTH_STATES, SUCCESS_MESSAGES } from "../constants";
import { AuthService } from "../services/authService";
import {
  AuthState,
  CaptchaInfo,
  LoginConfig,
  LoginInput,
  UserInfo,
} from "../types";
import { ErrorHandler, formatTime, safeAsync, TokenManager } from "../utils";
import { EncryptionUtils } from "../utils/encryption";
import { showSuccess } from "../utils/toast";

interface AuthStore extends AuthState {
  // 状态
  authStatus: keyof typeof AUTH_STATES;
  loginConfig: LoginConfig | null;
  captchaInfo: CaptchaInfo | null;

  // 操作方法
  login: (loginData: LoginInput) => Promise<boolean>;
  logout: () => Promise<void>;
  checkAuthStatus: () => Promise<void>;
  getUserInfo: () => Promise<void>;
  getLoginConfig: () => Promise<void>;
  getCaptcha: () => Promise<void>;
  clearError: () => void;

  // 内部方法
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  setUser: (user: UserInfo | null) => void;
  setToken: (token: string | null) => void;
  setAuthStatus: (status: keyof typeof AUTH_STATES) => void;
}

export const useAuthStore = create<AuthStore>((set, get) => ({
  // 初始状态
  isAuthenticated: false,
  user: null,
  token: null,
  isLoading: false,
  error: null,
  authStatus: "UNAUTHENTICATED",
  loginConfig: null,
  captchaInfo: null,

  // 设置加载状态
  setLoading: (loading: boolean) => {
    set({ isLoading: loading });
  },

  // 设置错误信息
  setError: (error: string | null) => {
    set({ error });
  },

  // 清除错误信息
  clearError: () => {
    set({ error: null });
  },

  // 设置用户信息
  setUser: (user: UserInfo | null) => {
    set({ user, isAuthenticated: !!user });
  },

  // 设置Token
  setToken: (token: string | null) => {
    set({ token });
  },

  // 设置认证状态
  setAuthStatus: (status: keyof typeof AUTH_STATES) => {
    set({ authStatus: status });
  },

  // 用户登录
  login: async (loginData: LoginInput): Promise<boolean> => {
    const { setLoading, setError, setUser, setToken, setAuthStatus } = get();

    try {
      setLoading(true);
      setError(null);

      // 使用RSA公钥加密密码
      const encryptedPassword = await EncryptionUtils.encryptPassword(
        loginData.password
      );

      const encryptedLoginData = {
        ...loginData,
        password: encryptedPassword,
      };

      // 调用登录API
      const [error, result] = await safeAsync(
        AuthService.login(encryptedLoginData)
      );

      if (error) {
        setError(error.message);
        return false;
      }

      if (!result?.accessToken) {
        setError("登录失败，请检查账号密码");
        return false;
      }

      // 存储Token
      await TokenManager.setToken(result.accessToken);
      if (result.refreshToken) {
        await TokenManager.setRefreshToken(result.refreshToken);
      }

      setToken(result.accessToken);
      setAuthStatus("AUTHENTICATED");

      // 获取用户信息
      await get().getUserInfo();

      // 显示成功提示
      // const timeGreeting = formatTime();
      // showSuccess(`${timeGreeting}，${SUCCESS_MESSAGES.LOGIN_SUCCESS}`);

      return true;
    } catch (error: any) {
      const appError = ErrorHandler.handle(error);
      setError(appError.message);
      return false;
    } finally {
      setLoading(false);
    }
  },

  // 用户登出
  logout: async (): Promise<void> => {
    const { setUser, setToken, setAuthStatus, setError } = get();

    try {
      // 调用服务端登出接口
      await AuthService.logout();

      // 清除本地存储
      await TokenManager.clearTokens();
      await AsyncStorage.removeItem("userInfo");

      // 重置状态
      setUser(null);
      setToken(null);
      setAuthStatus("UNAUTHENTICATED");
      setError(null);

      // showSuccess(SUCCESS_MESSAGES.LOGOUT_SUCCESS);
    } catch (error: any) {
      console.error("Logout error:", error);
      // 即使登出失败，也要清除本地状态
      await TokenManager.clearTokens();
      setUser(null);
      setToken(null);
      setAuthStatus("UNAUTHENTICATED");
    }
  },

  // 检查认证状态
  checkAuthStatus: async (): Promise<void> => {
    const { setLoading, setAuthStatus, setToken, getUserInfo } = get();

    try {
      setLoading(true);
      setAuthStatus("CHECKING");

      const token = await TokenManager.getToken();

      if (!token) {
        setAuthStatus("UNAUTHENTICATED");
        return;
      }

      // 检查Token是否过期
      const isExpired = await TokenManager.isTokenExpired(token);

      if (isExpired) {
        // Token过期，尝试刷新
        const refreshToken = await TokenManager.getRefreshToken();
        if (refreshToken) {
          try {
            const [error, result] = await safeAsync(
              AuthService.refreshToken(refreshToken)
            );
            if (result?.accessToken) {
              await TokenManager.setToken(result.accessToken);
              setToken(result.accessToken);
              setAuthStatus("AUTHENTICATED");
              await getUserInfo();
              return;
            }
          } catch (refreshError) {
            console.error("Token refresh failed:", refreshError);
          }
        }

        // 刷新失败，清除Token
        await TokenManager.clearTokens();
        setAuthStatus("UNAUTHENTICATED");
        return;
      }

      // Token有效，设置状态并获取用户信息
      setToken(token);
      setAuthStatus("AUTHENTICATED");
      await getUserInfo();
    } catch (error: any) {
      console.error("Check auth status error:", error);
      setAuthStatus("UNAUTHENTICATED");
    } finally {
      setLoading(false);
    }
  },

  // 获取用户信息
  getUserInfo: async (): Promise<void> => {
    const { setUser, setError } = get();

    try {
      const [error, userInfo] = await safeAsync(AuthService.getUserInfo());

      if (error) {
        setError(error.message);
        return;
      }

      if (userInfo) {
        // 存储用户信息到本地
        await AsyncStorage.setItem("userInfo", JSON.stringify(userInfo));
        setUser(userInfo);
      }
    } catch (error: any) {
      const appError = ErrorHandler.handle(error);
      setError(appError.message);
    }
  },

  // 获取登录配置
  getLoginConfig: async (): Promise<void> => {
    try {
      const [error, config] = await safeAsync(AuthService.getLoginConfig());

      if (!error && config) {
        set({ loginConfig: config });
      }
    } catch (error: any) {
      console.error("Get login config error:", error);
      // 获取配置失败时使用默认配置
      set({
        loginConfig: {
          captchaEnabled: true,
          secondVerEnabled: false,
        },
      });
    }
  },

  // 获取验证码
  getCaptcha: async (): Promise<void> => {
    try {
      const [error, captcha] = await safeAsync(AuthService.getCaptcha());

      if (!error && captcha) {
        set({ captchaInfo: captcha });
      }
    } catch (error: any) {
      console.error("Get captcha error:", error);
    }
  },
}));
