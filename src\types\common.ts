/**
 * 通用类型定义
 */

// 基础状态类型
export type LoadingState = "idle" | "loading" | "success" | "error";

// 主题类型
export type ThemeMode = "light" | "dark" | "system";

// 语言类型
export type Language = "zh-CN" | "en-US";

// 通用响应状态
export interface ResponseState<T = any> {
  data: T | null;
  loading: boolean;
  error: string | null;
}

// 分页参数
export interface PaginationParams {
  page: number;
  pageSize: number;
}

// 分页响应
export interface PaginationResponse<T = any> {
  items: T[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
}

// 选项类型
export interface Option<T = any> {
  label: string;
  value: T;
  disabled?: boolean;
}

// 表单验证规则
export interface ValidationRule {
  required?: boolean;
  message?: string;
  pattern?: RegExp;
  min?: number;
  max?: number;
  validator?: (value: any) => boolean | string;
}

// 表单字段配置
export interface FormField {
  name: string;
  label: string;
  type: "text" | "password" | "email" | "number" | "select" | "textarea";
  placeholder?: string;
  rules?: ValidationRule[];
  options?: Option[];
  disabled?: boolean;
}

// 应用配置
export interface AppConfig {
  theme: ThemeMode;
  language: Language;
  apiUrl: string;
  timeout: number;
}

// 错误信息
export interface ErrorInfo {
  code: string | number;
  message: string;
  details?: any;
  timestamp: number;
}

// 事件回调类型
export type EventCallback<T = any> = (data: T) => void;

// 异步操作结果
export type AsyncResult<T = any> = Promise<[Error | null, T | null]>;

// 键值对类型
export type KeyValuePair<T = any> = Record<string, T>;

// 深度可选类型
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

// 深度只读类型
export type DeepReadonly<T> = {
  readonly [P in keyof T]: T[P] extends object ? DeepReadonly<T[P]> : T[P];
};
