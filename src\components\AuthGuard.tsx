/**
 * 认证保护组件
 * 根据用户认证状态控制页面访问
 */

import { useAuthStore } from '@/src/stores';
import { useRouter, useSegments } from 'expo-router';
import React, { useEffect } from 'react';

/**
 * 认证保护组件
 * 根据Zustand中的认证状态，使用Expo Router的API来保护路由。
 */
export function AuthGuard({ children }: { children: React.ReactNode }) {
  const { isAuthenticated, authStatus } = useAuthStore();
  const segments = useSegments();
  const router = useRouter();

  useEffect(() => {
    // 等待认证状态检查完成
    if (authStatus === 'CHECKING') {
      return;
    }

    const isLoginPage = segments[0] === 'login';

    // 场景1: 用户未登录
    // 如果用户未认证，并且他当前不在登录页，那么将他重定向到登录页。
    if (!isAuthenticated && !isLoginPage) {
      router.replace('/login');
      return;
    }

    // 场景2: 用户已登录
    // 如果用户已认证，但他当前在登录页，那么将他重定向到应用主页。
    if (isAuthenticated && isLoginPage) {
      router.replace('/(tabs)');
    }
  }, [isAuthenticated, authStatus, segments, router]);

  // 在路由重定向前，不渲染任何内容，避免页面闪烁
  // 或者可以返回一个全局的加载动画
  return <>{children}</>;
}
