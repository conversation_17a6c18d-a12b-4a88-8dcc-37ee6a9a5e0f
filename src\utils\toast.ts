/**
 * Toast提示工具
 * 提供统一的用户反馈机制
 */

import { Alert } from 'react-native';

export interface ToastOptions {
  duration?: number;
  position?: 'top' | 'center' | 'bottom';
}

/**
 * 显示成功提示
 */
export const showSuccess = (message: string, options?: ToastOptions) => {
  Alert.alert('成功', message, [{ text: '确定' }]);
};

/**
 * 显示错误提示
 */
export const showError = (message: string, options?: ToastOptions) => {
  Alert.alert('错误', message, [{ text: '确定' }]);
};

/**
 * 显示警告提示
 */
export const showWarning = (message: string, options?: ToastOptions) => {
  Alert.alert('警告', message, [{ text: '确定' }]);
};

/**
 * 显示信息提示
 */
export const showInfo = (message: string, options?: ToastOptions) => {
  Alert.alert('提示', message, [{ text: '确定' }]);
};

/**
 * 显示确认对话框
 */
export const showConfirm = (
  title: string,
  message: string,
  onConfirm: () => void,
  onCancel?: () => void
) => {
  Alert.alert(
    title,
    message,
    [
      {
        text: '取消',
        style: 'cancel',
        onPress: onCancel,
      },
      {
        text: '确定',
        onPress: onConfirm,
      },
    ]
  );
};

/**
 * 显示加载提示
 * 注意：React Native的Alert不支持加载状态，这里提供接口以便后续扩展
 */
export const showLoading = (message: string = '加载中...') => {
  // 这里可以集成第三方Toast库，如react-native-toast-message
  console.log('Loading:', message);
};

/**
 * 隐藏加载提示
 */
export const hideLoading = () => {
  // 这里可以集成第三方Toast库
  console.log('Hide loading');
};
