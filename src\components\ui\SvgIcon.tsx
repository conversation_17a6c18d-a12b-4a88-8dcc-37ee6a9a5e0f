/**
 * SVG图标组件
 * 用于在React Native中正确显示SVG图标
 */

import React from 'react';
import { StyleSheet, View } from 'react-native';
import Svg, { Path } from 'react-native-svg';

interface SvgIconProps {
  name: 'prepay' | 'prepayreport' | 'dataplatform';
  size?: number;
  color?: string;
}

const SvgIcon: React.FC<SvgIconProps> = ({ name, size = 32, color = '#000' }) => {
  const renderIcon = () => {
    switch (name) {
      case 'prepay':
        return (
          <Svg width={size} height={size} viewBox="0 0 1024 1024">
            {/* 外圆环 */}
            <Path
              d="M512 958.8C265.7 958.8 65.2 758.4 65.2 512S265.7 65.2 512 65.2 958.8 265.6 958.8 512 758.3 958.8 512 958.8z m0-832c-212.4 0-385.2 172.8-385.2 385.2S299.6 897.2 512 897.2 897.2 724.4 897.2 512 724.4 126.8 512 126.8z"
              fill={color}
            />
            {/* 十字加号 */}
            <Path
              d="M681.5 478.5v-61.6h-97.7l82.1-82.1-43.6-43.5-109 109-109.1-109-43.6 43.5 82.1 82.1H342.5v61.6h138.7v61.7H342.5v61.6h138.7v131h61.6v-131h138.7v-61.6H542.8v-61.7z"
              fill={color}
            />
          </Svg>
        );
      
      case 'prepayreport':
        return (
          <Svg width={size} height={size} viewBox="0 0 1024 1024">
            {/* 文档头部 */}
            <Path
              d="M944 382.56h-138.24v-64H880v-160H144v160h77.6v64H80V94.72h864v287.84z"
              fill={color}
            />
            {/* 文档主体 */}
            <Path
              d="M512 929.28l-153.6-64L176 922.24V222.72h672v699.52L665.6 864z m-150.4-132.64l150.4 64 150.4-64 121.6 38.4V286.72H240v548.32z"
              fill={color}
            />
            {/* 文档线条 */}
            <Path
              d="M343.2 398.72h337.6v64h-337.6zM343.2 542.72h337.6v64h-337.6z"
              fill={color}
            />
          </Svg>
        );

      case 'dataplatform':
        return (
          <Svg width={size} height={size} viewBox="0 0 1024 1024">
            {/* 数据表格背景 */}
            <Path
              d="M128 192h768c17.7 0 32 14.3 32 32v576c0 17.7-14.3 32-32 32H128c-17.7 0-32-14.3-32-32V224c0-17.7 14.3-32 32-32z"
              fill={color}
              fillOpacity="0.1"
              stroke={color}
              strokeWidth="2"
            />
            {/* 表格分割线 */}
            <Path
              d="M160 288h704M160 384h704M160 480h704M160 576h704M160 672h704"
              stroke={color}
              strokeWidth="1.5"
              strokeLinecap="round"
            />
            {/* 垂直分割线 */}
            <Path
              d="M320 224v576M520 224v576M720 224v576"
              stroke={color}
              strokeWidth="1.5"
              strokeLinecap="round"
            />
            {/* 柱状图图标 */}
            <Path
              d="M240 640v-80h40v80h-40zM360 640v-120h40v120h-40zM480 640v-160h40v160h-40zM600 640v-100h40v100h-40zM720 640v-140h40v140h-40z"
              fill={color}
            />
            {/* 数据点连线 */}
            <Path
              d="M260 560l100-40 120-60 120 20 120-40"
              stroke={color}
              strokeWidth="3"
              fill="none"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
            {/* 数据点 */}
            <Path
              d="M260 560m-6 0a6 6 0 1 0 12 0 6 6 0 1 0-12 0zM360 520m-6 0a6 6 0 1 0 12 0 6 6 0 1 0-12 0zM480 460m-6 0a6 6 0 1 0 12 0 6 6 0 1 0-12 0zM600 480m-6 0a6 6 0 1 0 12 0 6 6 0 1 0-12 0zM720 440m-6 0a6 6 0 1 0 12 0 6 6 0 1 0-12 0z"
              fill={color}
            />
          </Svg>
        );

      default:
        return null;
    }
  };

  return (
    <View style={[styles.container, { width: size, height: size }]}>
      {renderIcon()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    justifyContent: 'center',
    alignItems: 'center',
  },
});

export default SvgIcon;
