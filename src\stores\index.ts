/**
 * 状态管理导出文件
 */

// 导出所有Store
export { useAppStore, useLanguage, useNetworkStatus, useTheme } from './appStore';
export { useAuthStore } from './authStore';
export { useLoading, useLoadingStore, withLoading } from './loadingStore';

// 导出持久化工具
export * from './persistence';

// 导出Store初始化函数
export const initializeStores = async () => {
  try {
    // 动态导入stores以避免循环依赖
    const { useAppStore } = await import('./appStore');
    const { useAuthStore } = await import('./authStore');

    // 初始化应用配置
    await useAppStore.getState().initializeApp();

    // 检查认证状态
    await useAuthStore.getState().checkAuthStatus();

    console.log('Stores initialized successfully');
  } catch (error) {
    console.error('Failed to initialize stores:', error);
  }
};
