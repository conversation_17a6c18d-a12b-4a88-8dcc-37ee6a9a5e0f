/**
 * Text 组件
 * 统一的文本组件，支持多种样式和主题
 */

import React from 'react';
import { Text as RNText, TextStyle } from 'react-native';
import { useTheme } from '../../stores/appStore';

interface TextProps {
  children: React.ReactNode;
  variant?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'body1' | 'body2' | 'caption' | 'overline';
  color?: 'primary' | 'secondary' | 'tertiary' | 'inverse' | 'disabled' | 'success' | 'warning' | 'error' | 'info';
  weight?: 'light' | 'normal' | 'medium' | 'semibold' | 'bold' | 'extrabold';
  align?: 'left' | 'center' | 'right' | 'justify';
  numberOfLines?: number;
  ellipsizeMode?: 'head' | 'middle' | 'tail' | 'clip';
  style?: TextStyle;
  testID?: string;
  onPress?: () => void;
}

const Text: React.FC<TextProps> = ({
  children,
  variant = 'body1',
  color = 'primary',
  weight = 'normal',
  align = 'left',
  numberOfLines,
  ellipsizeMode = 'tail',
  style,
  testID,
  onPress,
}) => {
  const { theme } = useTheme();

  // 获取文本样式
  const getTextStyle = (): TextStyle => {
    const baseStyle: TextStyle = {
      fontFamily: theme.typography.fontFamily.primary,
      textAlign: align,
    };

    // 变体样式
    const variantStyles: Record<string, TextStyle> = {
      h1: {
        fontSize: theme.typography.fontSize['6xl'],
        fontWeight: theme.typography.fontWeight.bold,
        lineHeight: theme.typography.fontSize['6xl'] * theme.typography.lineHeight.tight,
      },
      h2: {
        fontSize: theme.typography.fontSize['5xl'],
        fontWeight: theme.typography.fontWeight.bold,
        lineHeight: theme.typography.fontSize['5xl'] * theme.typography.lineHeight.tight,
      },
      h3: {
        fontSize: theme.typography.fontSize['4xl'],
        fontWeight: theme.typography.fontWeight.semibold,
        lineHeight: theme.typography.fontSize['4xl'] * theme.typography.lineHeight.tight,
      },
      h4: {
        fontSize: theme.typography.fontSize['3xl'],
        fontWeight: theme.typography.fontWeight.semibold,
        lineHeight: theme.typography.fontSize['3xl'] * theme.typography.lineHeight.normal,
      },
      h5: {
        fontSize: theme.typography.fontSize['2xl'],
        fontWeight: theme.typography.fontWeight.medium,
        lineHeight: theme.typography.fontSize['2xl'] * theme.typography.lineHeight.normal,
      },
      h6: {
        fontSize: theme.typography.fontSize.xl,
        fontWeight: theme.typography.fontWeight.medium,
        lineHeight: theme.typography.fontSize.xl * theme.typography.lineHeight.normal,
      },
      body1: {
        fontSize: theme.typography.fontSize.base,
        fontWeight: theme.typography.fontWeight.normal,
        lineHeight: theme.typography.fontSize.base * theme.typography.lineHeight.normal,
      },
      body2: {
        fontSize: theme.typography.fontSize.sm,
        fontWeight: theme.typography.fontWeight.normal,
        lineHeight: theme.typography.fontSize.sm * theme.typography.lineHeight.normal,
      },
      caption: {
        fontSize: theme.typography.fontSize.xs,
        fontWeight: theme.typography.fontWeight.normal,
        lineHeight: theme.typography.fontSize.xs * theme.typography.lineHeight.normal,
      },
      overline: {
        fontSize: theme.typography.fontSize.xs,
        fontWeight: theme.typography.fontWeight.medium,
        lineHeight: theme.typography.fontSize.xs * theme.typography.lineHeight.normal,
        textTransform: 'uppercase',
        letterSpacing: theme.typography.letterSpacing.wide,
      },
    };

    // 颜色样式
    const colorStyles: Record<string, { color: string }> = {
      primary: { color: theme.colors.text.primary },
      secondary: { color: theme.colors.text.secondary },
      tertiary: { color: theme.colors.text.tertiary },
      inverse: { color: theme.colors.text.inverse },
      disabled: { color: theme.colors.text.disabled },
      success: { color: theme.colors.success },
      warning: { color: theme.colors.warning },
      error: { color: theme.colors.error },
      info: { color: theme.colors.info },
    };

    // 字重样式
    const weightStyles: Record<string, { fontWeight: string }> = {
      light: { fontWeight: theme.typography.fontWeight.light },
      normal: { fontWeight: theme.typography.fontWeight.normal },
      medium: { fontWeight: theme.typography.fontWeight.medium },
      semibold: { fontWeight: theme.typography.fontWeight.semibold },
      bold: { fontWeight: theme.typography.fontWeight.bold },
      extrabold: { fontWeight: theme.typography.fontWeight.extrabold },
    };

    return {
      ...baseStyle,
      ...variantStyles[variant],
      ...colorStyles[color],
      ...weightStyles[weight],
    };
  };

  return (
    <RNText
      style={[getTextStyle(), style]}
      numberOfLines={numberOfLines}
      ellipsizeMode={ellipsizeMode}
      testID={testID}
      onPress={onPress}
    >
      {children}
    </RNText>
  );
};

export default Text;
