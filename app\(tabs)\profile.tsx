/**
 * 我的 Tab 页面
 * 用户个人信息和设置
 */

import FontAwesome from '@expo/vector-icons/FontAwesome';
import { useRouter } from 'expo-router';
import React from 'react';
import { Alert, StyleSheet, TouchableOpacity, View } from 'react-native';
import { Button, Card, Container, Screen, Text } from '../../src/components/ui';
import { useAuthStore } from '../../src/stores';
import { useTheme } from '../../src/stores/appStore';

const ProfileScreen: React.FC = () => {
  const { theme, themeMode } = useTheme();
  const { user, logout } = useAuthStore();
  const router = useRouter();

  // 主题选项配置
  const themeOptions = [
    { value: 'light', label: '浅色', icon: 'sun-o' },
    { value: 'dark', label: '深色', icon: 'moon-o' },
    { value: 'system', label: '跟随系统', icon: 'mobile' },
  ];

  // 获取当前主题显示信息
  const getCurrentThemeInfo = () => {
    const option = themeOptions.find(opt => opt.value === themeMode);
    return option || themeOptions[0];
  };

  const handleLogout = async () => {
    Alert.alert(
      '确认退出',
      '您确定要退出登录吗？',
      [
        {
          text: '取消',
          style: 'cancel',
        },
        {
          text: '确定',
          style: 'destructive',
          onPress: async () => {
            await logout();
          },
        },
      ]
    );
  };

  // 设置选项类型定义
  interface SettingOption {
    id: number;
    title: string;
    icon: string;
    subtitle?: string;
    onPress?: () => void;
    isNavigable?: boolean; // 新增一个标志，用于判断是否是导航项
  }

  // 设置选项
  const settingOptions: SettingOption[] = [
    {
      id: 1,
      title: '账户设置',
      icon: 'cog',
      onPress: () => Alert.alert('提示', '账户设置功能开发中'),
      isNavigable: true,
    },
    {
      id: 2,
      title: '主题设置',
      icon: getCurrentThemeInfo().icon,
      subtitle: getCurrentThemeInfo().label,
      onPress: () => router.push('/theme-settings'),
      isNavigable: true,
    },
    {
      id: 3,
      title: '通知设置',
      icon: 'bell',
      onPress: () => Alert.alert('提示', '通知设置功能开发中'),
      isNavigable: true,
    },
    {
      id: 4,
      title: '隐私设置',
      icon: 'shield',
      onPress: () => Alert.alert('提示', '隐私设置功能开发中'),
      isNavigable: true,
    },
    {
      id: 5,
      title: '帮助与反馈',
      icon: 'question-circle',
      onPress: () => Alert.alert('提示', '帮助与反馈功能开发中'),
      isNavigable: true,
    },
    {
      id: 6,
      title: '关于应用',
      icon: 'info-circle',
      onPress: () => Alert.alert('关于', 'HuiLink移动应用 v1.0.0'),
      isNavigable: true,
    },
  ];

  return (
    <Screen backgroundColor={theme.colors.background.secondary}>
      <Container flex={1} padding={6}>
        {/* 用户信息卡片 */}
        <Card padding={6} elevation={2} style={{ marginBottom: theme.spacing[4] }}>
          <Container>
            <View style={styles.userHeader}>
              <View style={styles.avatarContainer}>
                <FontAwesome name="user-circle" size={60} color={theme.colors.primary} />
              </View>
              <View style={styles.userInfo}>
                <Text variant="h4" weight="semibold" style={{ marginBottom: theme.spacing[1] }}>
                  {user?.realName || user?.account || '用户'}
                </Text>
                <Text variant="body1" color="secondary">
                  {user?.account}
                </Text>
                {user?.orgName && (
                  <Text variant="caption" color="secondary" style={{ marginTop: theme.spacing[1] }}>
                    {user.orgName}
                  </Text>
                )}
              </View>
            </View>
          </Container>
        </Card>

        {/* 详细信息 */}
        <Card padding={6} elevation={2} style={{ marginBottom: theme.spacing[4] }}>
          <Container>
            <Text variant="h6" weight="semibold" style={{ marginBottom: theme.spacing[4] }}>
              个人信息
            </Text>

            <View style={styles.infoRow}>
              <Text variant="body1" color="secondary" style={styles.label}>
                账号：
              </Text>
              <Text variant="body1">{user?.account}</Text>
            </View>

            <View style={styles.infoRow}>
              <Text variant="body1" color="secondary" style={styles.label}>
                姓名：
              </Text>
              <Text variant="body1">{user?.realName || '未设置'}</Text>
            </View>

            {user?.orgName && (
              <View style={styles.infoRow}>
                <Text variant="body1" color="secondary" style={styles.label}>
                  组织：
                </Text>
                <Text variant="body1">{user.orgName}</Text>
              </View>
            )}

            {user?.posName && (
              <View style={styles.infoRow}>
                <Text variant="body1" color="secondary" style={styles.label}>
                  职位：
                </Text>
                <Text variant="body1">{user.posName}</Text>
              </View>
            )}
          </Container>
        </Card>

        {/* 设置选项 */}
        <Card padding={6} elevation={2} style={{ marginBottom: theme.spacing[4] }}>
          <Container>
            <Text variant="h6" weight="semibold" style={{ marginBottom: theme.spacing[4] }}>
              设置
            </Text>

            {settingOptions.map((option, index) => (
              <View key={option.id}>
                <TouchableOpacity
                  style={[
                    styles.settingItem,
                    {
                      borderBottomColor: theme.colors.border.tertiary,
                      backgroundColor: 'transparent'
                    },
                    index === settingOptions.length - 1 && { borderBottomWidth: 0 }
                  ]}
                  onPress={option.onPress}
                  activeOpacity={0.6}
                >
                  <View style={styles.settingLeft}>
                    <View style={[
                      styles.settingIconContainer,
                      { backgroundColor: theme.colors.primary + '15' }
                    ]}>
                      <FontAwesome
                        name={option.icon as any}
                        size={18}
                        color={theme.colors.primary}
                      />
                    </View>
                    <View style={styles.settingTextContainer}>
                      <Text variant="body1">{option.title}</Text>
                    </View>
                  </View>
                  <View style={styles.settingRight}>
                    {option.subtitle && (
                      <Text variant="body2" color="secondary" style={{ marginRight: 8 }}>
                        {option.subtitle}
                      </Text>
                    )}
                    {option.isNavigable && (
                      <FontAwesome
                        name="chevron-right"
                        size={14}
                        color={theme.colors.text.tertiary}
                      />
                    )}
                  </View>
                </TouchableOpacity>
              </View>
            ))}
          </Container>
        </Card>

        {/* 退出登录 */}
        <Container style={{ marginTop: 'auto' }}>
          <Button
            title="退出登录"
            variant="outline"
            onPress={handleLogout}
            style={styles.logoutButton}
          />
        </Container>
      </Container>
    </Screen>
  );
};

const styles = StyleSheet.create({
  userHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatarContainer: {
    marginRight: 16,
  },
  userInfo: {
    flex: 1,
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 12,
  },
  label: {
    minWidth: 60,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingVertical: 16,
    paddingHorizontal: 4,
    borderBottomWidth: 1,
    borderRadius: 8,
    marginBottom: 2,
  },
  settingLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  settingRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  settingIconContainer: {
    width: 36,
    height: 36,
    borderRadius: 18,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  settingTextContainer: {
    // Removed flex: 1 to allow the right side to align properly
  },
  logoutButton: {
    marginBottom: 20,
  },
});

export default ProfileScreen;