/**
 * HTTP客户端配置
 * HuiLink移动应用的网络请求配置
 */

import AsyncStorage from '@react-native-async-storage/async-storage';
import axios, { AxiosError, AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { API_CONFIG, HTTP_STATUS, TOKEN_CONFIG } from '../constants';
import { ApiResponse } from '../types';
import { showError } from '../utils/toast';
import { TokenManager } from '../utils/tokenManager';

// 创建axios实例
const httpClient: AxiosInstance = axios.create({
  baseURL: API_CONFIG.BASE_URL,
  timeout: API_CONFIG.TIMEOUT,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器
httpClient.interceptors.request.use(
  async (config: AxiosRequestConfig) => {
    try {
      // 获取本地token
      const token = await TokenManager.getToken();
      
      if (token && config.headers) {
        // 添加Authorization头
        config.headers['Authorization'] = `Bearer ${token}`;
        
        // 检查token是否过期
        const isExpired = await TokenManager.isTokenExpired(token);
        if (isExpired) {
          // 尝试刷新token
          const refreshToken = await TokenManager.getRefreshToken();
          if (refreshToken && config.headers) {
            config.headers['X-Authorization'] = `Bearer ${refreshToken}`;
          }
        }
      }
      
      // 添加语言头（如果有设置）
      const language = await AsyncStorage.getItem('language');
      if (language && config.headers) {
        config.headers['Accept-Language'] = language;
      }
      
      return config;
    } catch (error) {
      console.error('Request interceptor error:', error);
      return config;
    }
  },
  (error: AxiosError) => {
    console.error('Request error:', error);
    return Promise.reject(error);
  }
);

// 响应拦截器
httpClient.interceptors.response.use(
  async (response: AxiosResponse<ApiResponse>) => {
    const { status, data, headers } = response;
    
    // 处理401未授权
    if (status === HTTP_STATUS.UNAUTHORIZED) {
      await TokenManager.clearTokens();
      // 这里可以触发重新登录逻辑
      return Promise.reject(new Error('Unauthorized'));
    }
    
    // 处理其他HTTP错误状态
    if (status >= 400) {
      const errorMessage = data?.message || 'Request Error';
      return Promise.reject(new Error(errorMessage));
    }
    
    // 处理业务逻辑错误
    if (data && data.hasOwnProperty('code') && data.code !== HTTP_STATUS.OK) {
      const errorMessage = typeof data.message === 'object' 
        ? JSON.stringify(data.message) 
        : data.message || 'Business Logic Error';
      
      // 特殊处理401业务错误
      if (data.code === HTTP_STATUS.UNAUTHORIZED) {
        await TokenManager.clearTokens();
        return Promise.reject(new Error('Token expired'));
      }
      
      return Promise.reject(new Error(errorMessage));
    }
    
    // 处理token刷新
    const newAccessToken = headers[TOKEN_CONFIG.ACCESS_TOKEN_KEY];
    const newRefreshToken = headers[TOKEN_CONFIG.REFRESH_TOKEN_KEY];
    
    if (newAccessToken === 'invalid_token') {
      await TokenManager.clearTokens();
      return Promise.reject(new Error('Invalid token'));
    }
    
    if (newRefreshToken && newAccessToken && newAccessToken !== 'invalid_token') {
      await TokenManager.setToken(newAccessToken);
      await TokenManager.setRefreshToken(newRefreshToken);
    }
    
    return response;
  },
  async (error: AxiosError) => {
    // 处理网络错误
    if (error.response) {
      const { status } = error.response;
      
      if (status === HTTP_STATUS.UNAUTHORIZED) {
        await TokenManager.clearTokens();
        showError('登录已过期，请重新登录');
      } else {
        showError(error.message || '网络请求失败');
      }
    } else if (error.request) {
      showError('网络连接失败，请检查网络设置');
    } else {
      showError(error.message || '请求配置错误');
    }
    
    return Promise.reject(error);
  }
);

export default httpClient;
