/**
 * 帆软系统认证工具
 * 实现RSA加密的单点登录功能
 */

import forge from "node-forge";

/**
 * 帆软认证配置接口
 */
export interface FanruanAuthConfig {
  serverUrl: string;
  projectName: string;
  useEncryption: boolean;
  publicKey?: string;
  enableTimeout?: boolean;
}

/**
 * 单点登录结果接口
 */
export interface SSOResult {
  success: boolean;
  url?: string;
  error?: string;
}

/**
 * RSA加密用户名
 * @param username 用户名
 * @param publicKeyPem RSA公钥（PEM格式）
 * @returns 加密并编码后的字符串
 */
export const encryptUsername = (
  username: string,
  publicKeyPem: string
): string => {
  try {
    // 验证公钥格式
    if (!publicKeyPem || !publicKeyPem.includes("BEGIN PUBLIC KEY")) {
      throw new Error("无效的RSA公钥格式");
    }

    // 从PEM格式创建公钥对象
    const publicKey = forge.pki.publicKeyFromPem(publicKeyPem);

    // 使用PKCS1填充加密用户名（匹配Java代码的默认RSA实现）
    const encrypted = publicKey.encrypt(username, "RSAES-PKCS1-V1_5");

    // 转换为Base64
    const base64Encrypted = forge.util.encode64(encrypted);

    // URL编码
    const urlEncoded = encodeURIComponent(base64Encrypted);

    return urlEncoded;
  } catch (error) {
    throw new Error(
      `用户名加密失败: ${error instanceof Error ? error.message : "未知错误"}`
    );
  }
};

/**
 * 生成带超时的ssoToken
 * @param username 用户名
 * @param publicKeyPem RSA公钥
 * @returns 加密的ssoToken
 */
export const generateSSOTokenWithTimeout = (
  username: string,
  publicKeyPem: string
): string => {
  try {
    // 获取当前时间戳（毫秒）
    const timestamp = Date.now();

    // 组合用户名和时间戳
    const payload = `${username}|${timestamp}`;

    // 加密组合后的数据
    return encryptUsername(payload, publicKeyPem);
  } catch (error) {
    console.error("生成超时ssoToken失败:", error);
    throw new Error(
      `生成超时ssoToken失败: ${
        error instanceof Error ? error.message : "未知错误"
      }`
    );
  }
};

/**
 * 生成帆软单点登录URL
 * @param config 帆软认证配置
 * @param username 用户名
 * @returns 单点登录结果
 */
export const generateFanruanSSOUrl = (
  config: FanruanAuthConfig,
  username: string
): SSOResult => {
  try {
    // 验证必要参数
    if (!config.serverUrl || !config.projectName || !username) {
      return {
        success: false,
        error: "缺少必要的配置参数或用户名",
      };
    }

    // 构建基础URL
    const baseUrl = `${config.serverUrl}/${config.projectName}/decision`;

    if (config.useEncryption) {
      // 方案二：使用加密
      if (!config.publicKey) {
        return {
          success: false,
          error: "启用加密但未提供RSA公钥",
        };
      }

      try {
        let ssoToken: string;

        if (config.enableTimeout) {
          // 生成带超时的ssoToken
          ssoToken = generateSSOTokenWithTimeout(username, config.publicKey);
        } else {
          // 生成普通的ssoToken
          ssoToken = encryptUsername(username, config.publicKey);
        }

        const ssoUrl = `${baseUrl}?ssoToken=${ssoToken}`;

        return {
          success: true,
          url: ssoUrl,
        };
      } catch (encryptError) {
        return {
          success: false,
          error: `加密失败: ${
            encryptError instanceof Error ? encryptError.message : "未知错误"
          }`,
        };
      }
    } else {
      // 方案一：不加密（不推荐用于生产环境）
      const ssoUrl = `${baseUrl}?fine_username=${encodeURIComponent(username)}`;

      return {
        success: true,
        url: ssoUrl,
      };
    }
  } catch (error) {
    console.error("生成帆软SSO URL失败:", error);
    return {
      success: false,
      error: `生成SSO URL失败: ${
        error instanceof Error ? error.message : "未知错误"
      }`,
    };
  }
};

/**
 * 验证RSA公钥格式
 * @param publicKeyPem RSA公钥字符串
 * @returns 是否为有效格式
 */
export const validatePublicKey = (publicKeyPem: string): boolean => {
  try {
    if (!publicKeyPem || typeof publicKeyPem !== "string") {
      return false;
    }

    // 检查PEM格式标识
    if (
      !publicKeyPem.includes("BEGIN PUBLIC KEY") ||
      !publicKeyPem.includes("END PUBLIC KEY")
    ) {
      return false;
    }

    // 尝试解析公钥
    forge.pki.publicKeyFromPem(publicKeyPem);
    return true;
  } catch (error) {
    console.error("公钥验证失败:", error);
    return false;
  }
};

/**
 * 测试加密解密功能
 * @param publicKeyPem RSA公钥
 * @param testString 测试字符串
 * @returns 测试结果
 */
export const testEncryption = (
  publicKeyPem: string,
  testString: string = "test"
): { success: boolean; error?: string } => {
  try {
    const encrypted = encryptUsername(testString, publicKeyPem);

    if (!encrypted || encrypted.length === 0) {
      return {
        success: false,
        error: "加密结果为空",
      };
    }

    return {
      success: true,
    };
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : "测试失败",
    };
  }
};

/**
 * 帆软认证工具类
 */
export class FanruanAuthUtils {
  private config: FanruanAuthConfig;

  constructor(config: FanruanAuthConfig) {
    this.config = config;
  }

  /**
   * 更新配置
   */
  updateConfig(config: Partial<FanruanAuthConfig>): void {
    this.config = { ...this.config, ...config };
  }

  /**
   * 获取当前配置
   */
  getConfig(): FanruanAuthConfig {
    return { ...this.config };
  }

  /**
   * 生成单点登录URL
   */
  generateSSOUrl(username: string): SSOResult {
    return generateFanruanSSOUrl(this.config, username);
  }

  /**
   * 验证配置
   */
  validateConfig(): { valid: boolean; errors: string[] } {
    const errors: string[] = [];

    if (!this.config.serverUrl) {
      errors.push("缺少服务器地址");
    }

    if (!this.config.projectName) {
      errors.push("缺少工程名称");
    }

    if (this.config.useEncryption && !this.config.publicKey) {
      errors.push("启用加密但未提供RSA公钥");
    }

    if (this.config.publicKey && !validatePublicKey(this.config.publicKey)) {
      errors.push("RSA公钥格式无效");
    }

    return {
      valid: errors.length === 0,
      errors,
    };
  }
}

/**
 * 比较加密结果
 * @param username 用户名
 * @param publicKeyPem RSA公钥
 * @param expectedResult 期望的加密结果（Base64格式，未URL编码）
 * @returns 比较结果
 */
export const compareEncryptionResult = (
  username: string,
  publicKeyPem: string,
  expectedResult: string
): {
  match: boolean;
  jsResult: string;
  expectedResult: string;
  jsResultDecoded: string;
} => {
  try {
    // 生成JavaScript版本的加密结果
    const jsEncrypted = encryptUsername(username, publicKeyPem);

    // 解码JavaScript结果以便比较
    const jsDecoded = decodeURIComponent(jsEncrypted);

    const match = jsDecoded === expectedResult;

    return {
      match,
      jsResult: jsEncrypted,
      expectedResult,
      jsResultDecoded: jsDecoded,
    };
  } catch (error) {
    return {
      match: false,
      jsResult: "",
      expectedResult,
      jsResultDecoded: "",
    };
  }
};
