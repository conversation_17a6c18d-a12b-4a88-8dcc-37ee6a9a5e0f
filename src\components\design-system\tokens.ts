/**
 * 设计令牌 (Design Tokens)
 * HuiLink移动应用的设计系统基础
 */

// 颜色系统
export const Colors = {
  // 主色调 - 优化的蓝色渐变
  primary: {
    50: "#EBF8FF",
    100: "#BEE3F8",
    200: "#90CDF4",
    300: "#63B3ED",
    400: "#4299E1",
    500: "#409EFF", // 主色
    600: "#3182CE",
    700: "#2B77CB",
    800: "#2C5282",
    900: "#2A4365",
  },

  // 渐变色
  gradients: {
    primary: "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
    primaryBlue: "linear-gradient(135deg, #409EFF 0%, #3182CE 100%)",
    primarySoft: "linear-gradient(135deg, #667eea 0%, #409EFF 100%)",
    success: "linear-gradient(135deg, #67C23A 0%, #85ce61 100%)",
    warning: "linear-gradient(135deg, #E6A23C 0%, #ebb563 100%)",
    error: "linear-gradient(135deg, #F56C6C 0%, #f78989 100%)",
  },

  // 辅助色
  secondary: {
    50: "#F7FAFC",
    100: "#EDF2F7",
    200: "#E2E8F0",
    300: "#CBD5E0",
    400: "#A0AEC0",
    500: "#718096",
    600: "#4A5568",
    700: "#2D3748",
    800: "#1A202C",
    900: "#171923",
  },

  // 功能色
  success: {
    50: "#F0FFF4",
    100: "#C6F6D5",
    200: "#9AE6B4",
    300: "#68D391",
    400: "#48BB78",
    500: "#67C23A", // 成功色
    600: "#38A169",
    700: "#2F855A",
    800: "#276749",
    900: "#22543D",
  },

  warning: {
    50: "#FFFBEB",
    100: "#FEF3C7",
    200: "#FDE68A",
    300: "#FCD34D",
    400: "#FBBF24",
    500: "#E6A23C", // 警告色
    600: "#D97706",
    700: "#B45309",
    800: "#92400E",
    900: "#78350F",
  },

  error: {
    50: "#FEF2F2",
    100: "#FEE2E2",
    200: "#FECACA",
    300: "#FCA5A5",
    400: "#F87171",
    500: "#F56C6C", // 错误色
    600: "#DC2626",
    700: "#B91C1C",
    800: "#991B1B",
    900: "#7F1D1D",
  },

  info: {
    50: "#F0F9FF",
    100: "#E0F2FE",
    200: "#BAE6FD",
    300: "#7DD3FC",
    400: "#38BDF8",
    500: "#909399", // 信息色
    600: "#0284C7",
    700: "#0369A1",
    800: "#075985",
    900: "#0C4A6E",
  },

  // 文字颜色
  text: {
    primary: "#303133",
    regular: "#606266",
    secondary: "#909399",
    placeholder: "#C0C4CC",
    disabled: "#C0C4CC",
    inverse: "#FFFFFF",
  },

  // 背景色
  background: {
    primary: "#FFFFFF",
    secondary: "#F5F7FA",
    tertiary: "#FAFAFA",
    overlay: "rgba(0, 0, 0, 0.5)",
    disabled: "#F5F7FA",
  },

  // 边框色
  border: {
    base: "#DCDFE6",
    light: "#E4E7ED",
    lighter: "#EBEEF5",
    extraLight: "#F2F6FC",
    dark: "#D4D7DE",
    darker: "#CDD0D6",
  },

  // 透明度
  opacity: {
    disabled: 0.5,
    hover: 0.8,
    pressed: 0.6,
    overlay: 0.5,
  },
};

// 字体系统
export const Typography = {
  // 字体族
  fontFamily: {
    primary: "System", // 系统默认字体
    mono: "Courier New", // 等宽字体
  },

  // 字体大小 - 优化移动端可读性
  fontSize: {
    xs: 12, // 从 10 增加到 12
    sm: 14, // 从 12 增加到 14
    base: 16, // 从 14 增加到 16
    lg: 18, // 从 16 增加到 18
    xl: 20, // 从 18 增加到 20
    "2xl": 22, // 从 20 增加到 22
    "3xl": 26, // 从 24 增加到 26
    "4xl": 30, // 从 28 增加到 30
    "5xl": 34, // 从 32 增加到 34
    "6xl": 38, // 从 36 增加到 38
  },

  // 字体粗细
  fontWeight: {
    light: "300",
    normal: "400",
    medium: "500",
    semibold: "600",
    bold: "700",
    extrabold: "800",
  },

  // 行高
  lineHeight: {
    tight: 1.2,
    normal: 1.4,
    relaxed: 1.6,
    loose: 1.8,
  },

  // 字母间距
  letterSpacing: {
    tight: -0.5,
    normal: 0,
    wide: 0.5,
    wider: 1,
  },
};

// 间距系统
export const Spacing = {
  0: 0,
  1: 4,
  2: 8,
  3: 12,
  4: 16,
  5: 20,
  6: 24,
  7: 28,
  8: 32,
  9: 36,
  10: 40,
  12: 48,
  14: 56,
  16: 64,
  20: 80,
  24: 96,
  28: 112,
  32: 128,
};

// 圆角系统
export const BorderRadius = {
  none: 0,
  sm: 4,
  base: 8,
  md: 12,
  lg: 16,
  xl: 20,
  "2xl": 24,
  "3xl": 32,
  full: 9999,
};

// 阴影系统
export const Shadows = {
  none: "none",
  sm: {
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  base: {
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  md: {
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 8,
    elevation: 4,
  },
  lg: {
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.2,
    shadowRadius: 16,
    elevation: 8,
  },
  xl: {
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 12 },
    shadowOpacity: 0.25,
    shadowRadius: 24,
    elevation: 12,
  },
  // 新增特殊阴影效果
  card: {
    shadowColor: "#409EFF",
    shadowOffset: { width: 0, height: 8 },
    shadowOpacity: 0.12,
    shadowRadius: 24,
    elevation: 6,
  },
  button: {
    shadowColor: "#409EFF",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 12,
    elevation: 4,
  },
  input: {
    shadowColor: "#409EFF",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 2,
  },
};

// 动画时长
export const Duration = {
  fast: 150,
  normal: 250,
  slow: 350,
  slower: 500,
};

// 缓动函数
export const Easing = {
  linear: "linear",
  ease: "ease",
  easeIn: "ease-in",
  easeOut: "ease-out",
  easeInOut: "ease-in-out",
};

// 断点系统
export const Breakpoints = {
  sm: 576,
  md: 768,
  lg: 992,
  xl: 1200,
};

// Z-index层级
export const ZIndex = {
  hide: -1,
  auto: "auto",
  base: 0,
  docked: 10,
  dropdown: 1000,
  sticky: 1100,
  banner: 1200,
  overlay: 1300,
  modal: 1400,
  popover: 1500,
  skipLink: 1600,
  toast: 1700,
  tooltip: 1800,
};
