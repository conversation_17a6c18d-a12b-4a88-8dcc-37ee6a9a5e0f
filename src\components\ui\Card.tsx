/**
 * Card 组件
 * 卡片容器组件，提供阴影和圆角效果
 */

import React from 'react';
import { View, ViewStyle } from 'react-native';
import { useTheme } from '../../stores/appStore';
import { CardProps } from '../../types';

const Card: React.FC<CardProps> = ({
  children,
  padding = 4,
  margin = 0,
  elevation = 2,
  borderRadius,
  style,
  testID,
}) => {
  const { theme } = useTheme();

  // 获取卡片样式
  const getCardStyle = (): ViewStyle => {
    const baseStyle: ViewStyle = {
      backgroundColor: theme.colors.surface.primary,
      borderRadius: borderRadius !== undefined
        ? (typeof borderRadius === 'number' ? borderRadius : theme.borderRadius[borderRadius as keyof typeof theme.borderRadius])
        : theme.borderRadius.base,
      padding: typeof padding === 'number' ? padding : theme.spacing[padding as keyof typeof theme.spacing],
      margin: typeof margin === 'number' ? margin : theme.spacing[margin as keyof typeof theme.spacing],
    };

    // 添加阴影效果
    const shadowStyles = {
      0: {},
      1: theme.shadows.sm,
      2: theme.shadows.base,
      3: theme.shadows.md,
      4: theme.shadows.lg,
      5: theme.shadows.xl,
    };

    const shadowStyle = shadowStyles[elevation as keyof typeof shadowStyles] || theme.shadows.base;

    return {
      ...baseStyle,
      ...shadowStyle,
    };
  };

  return (
    <View style={[getCardStyle(), style]} testID={testID}>
      {children}
    </View>
  );
};

export default Card;
